﻿using namespace System.Collections.Generic
using namespace System.Management.Automation

function Show-ImagePopup {
    [CmdletBinding()]
    param (
        [Parameter(
            Mandatory,
            Position = 0,
            ValueFromPipeline,
            ValueFromPipelineByPropertyName,
            HelpMessage = "Literal path to one or more locations to process."
        )]
        [ValidateScript({
            # Throw on invalid path
            if(-not(Test-Path -Path $_ -IsValid)){ throw "The directory/file name '$_' is not valid." }
            # Throw on wildcard characters
            if($_ -match '[\?\*]'){
                throw "Wildcard characters *, ? are not acceptable."
            }
            $true
        })]
        [Alias('PSPath')]
        [ValidateNotNullOrEmpty()]
        [String[]] $LiteralPath
    )

    begin {
        Add-Type -AssemblyName System.Windows.Forms
        $validFiles = @('.jpg','.jpeg','.png','.bmp','.gif','.tif','.tiff')
        $filesToProcess = [HashSet[String]]@()
    }

    process {
        $resolvedPaths = [HashSet[String]]@()
        foreach ($curPath in $LiteralPath) {
            if (!(Test-Path -LiteralPath $curPath)) {
                $ex = [ItemNotFoundException]::new("Cannot find path '$curPath' because it does not exist.")
                $category = [ErrorCategory]::ObjectNotFound
                $errRecord = [ErrorRecord]::New($ex,'PathNotFound',$category,$curPath)
                $psCmdlet.WriteError($errRecord)
                continue
            }
            # Resolve any relative paths
            $null = $resolvedPaths.Add($psCmdlet.SessionState.Path.GetUnresolvedProviderPathFromPSPath($curPath))
        }
        foreach ($path in $resolvedPaths) {
            $item = Get-Item -LiteralPath $path -Force
            if (-not $item.PSIsContainer) {
                if($item.Extension -in $validFiles){
                    $null = $filesToProcess.Add($item.FullName)
                }
            }
        }
    }
    end {
        foreach ($imageFile in $filesToProcess) {
            $ImageFileInfo = Get-Item -Path $imageFile
            $Image = [Drawing.Image]::FromFile($ImageFileInfo)

            $PictureBox = [Windows.Forms.PictureBox]::new()
            $PictureBox.Size = $Image.Size
            $PictureBox.Image = $Image

            $Form = [Windows.Forms.Form]::new()
            $Form.Size = $Image.Size
            $Form.Controls.Add($PictureBox)
            $Form.ShowDialog()
        }
    }
}