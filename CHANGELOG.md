# Changelog
All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]
### Added
- New function 'Use-PythonInstallRequirementsToVENV'
- New function 'Use-PythonFreezeVENVToRequirements'
- New function 'Use-PythonActivateVENVInFolder'
- New function 'Update-EnvironmentVariables'
- New function 'Update-TTFAutohintFont'
- New function 'Update-PythonPIPInVENV'
- New function 'Update-PythonPIPGlobally'
- New function 'Update-PythonPackagesInVENV'
- New function 'Update-NVMGlobalNodePackagesByVersion'
- New function 'Uninstall-NVMNodeGlobalPackages'
- New function 'Test-ValidWildcardPath'
- New function 'Test-ValidLiteralPath'
- New function 'Test-URLIsValid'
- New function 'Test-PathIsValid'
- New function 'Test-PathIsLikelyFile'
- New function 'Test-PathIsLikelyDirectory'
- New function 'Test-PathContainsWildcards'
- New function 'Confirm-NVMForWindowsIsInstalled'
- New function 'Test-NodeInstalled'
- New function 'Test-ModuleIsLoaded'
- New function 'Test-IsValidGUID'
- New function 'Test-IsFontVariable'
- New function 'Test-IsElevated'
- New function 'Test-FunctionAvailability'
- New function 'Test-FileIsLocked'
- New function 'Test-DirectoryIsProtected'
- New function 'Test-DirectoryIsEmpty'
- New function 'Test-DirectoryContainsPwshFiles'
- New function 'Switch-NodeVersionsWithNVM'
- New function 'Stop-WSL'
- New function 'Stop-PwshProcesses'
- New function 'Stop-PowershellProcesses'
- New function 'Stop-AdobeProcesses'
- New function 'Split-StringByDelimiterAndCombineLines'
- New function 'Split-StringByDelimiter'
- New function 'Split-DirectoryContentsToSubfolders'
- New function 'Show-UWPToastNotification'
- New function 'Show-SystemOSClockResolution'
- New function 'Show-SpectreCountdownTimer'
- New function 'Show-NVMNodeGlobalPackages'
- New function 'Show-ModuleDetails'
- New function 'Show-HorizontalLineInConsole'
- New function 'Show-FilesBasedOnAgeInDirectory'
- New function 'Show-CountdownTimer'
- New function 'Set-WindowsFolderIcon'
- New function 'Set-FontEmbedLevelToUnrestricted'
- New function 'Search-GoogleIt'
- New function 'Save-WindowsOpenDirectories'
- New function 'Save-RandomDataToFiles'
- New function 'Save-RandomDataToFile'
- New function 'Save-PowershellGalleryNupkg'
- New function 'Save-FontsToFolder'
- New function 'Save-FolderToSubfolderByWord'
- New function 'Save-FoldersInCurrentDirectory'
- New function 'Save-FilesToFolderByWord'
- New function 'Save-Base64StringToFile'
- New function 'Resolve-WindowsSIDToIdentifier'
- New function 'Resolve-SymbolicLinks'
- New function 'Resolve-RelativePath'
- New function 'Resolve-PathType'
- New function 'Resolve-NPMPackageString'
- New function 'Request-ExplorerRefreshV2'
- New function 'Request-ExplorerRefreshV3'
- New function 'Request-ExplorerRefreshV1'
- New function 'Rename-SanitizeFilenamesInFolder'
- New function 'Rename-SanitizeFilenames'
- New function 'Rename-RandomizeFilenames'
- New function 'Rename-ImageDensityMultiplerToActual'
- New function 'Rename-FontsWithFTCLI'
- New function 'Remove-InvalidFilenameCharacters'
- New function 'Remove-GitHubRepository'
- New function 'Remove-EmptyDirectories'
- New function 'Remove-ANSICodesFromString'
- New function 'Register-WindowsDLLorOCX'
- New function 'Read-PressAnyKeyToContinue'
- New function 'Out-FileHash'
- New function 'Open-WindowsExplorerTo'
- New function 'New-TempDirectory'
- New function 'New-LogSpectre'
- New function 'New-Log'
- New function 'Move-VariableFont'
- New function 'Move-FontFamiliesToSubfolders'
- New function 'Join-StringByNewlinesWithDelimiter'
- New function 'Invoke-VBMessageBox'
- New function 'Invoke-SelfElevate'
- New function 'Invoke-SaveFileDialog'
- New function 'Invoke-OpenFolderDialog'
- New function 'Invoke-OpenFileDialog'
- New function 'Invoke-OokiiTaskDialog'
- New function 'Invoke-OokiiPasswordDialog'
- New function 'Invoke-OokiiInputDialog'
- New function 'Invoke-Ngen'
- New function 'Invoke-GUIMessageBox'
- New function 'Invoke-GalleryDLSaveGallery'
- New function 'Invoke-AndWaitForProcessOpen'
- New function 'Install-PythonGlobalPackages'
- New function 'Install-NVMNodeGlobalPackages'
- New function 'Install-NodeGlobalPackagesInteractive'
- New function 'Group-FontsByWidth'
- New function 'Get-WindowsWSLDistributionInfo'
- New function 'Get-WindowsVersionDetails'
- New function 'Get-WindowsProductKey'
- New function 'Get-WindowsProcessOverview'
- New function 'Get-WindowsOSArchitecture'
- New function 'Get-WindowsOpenDirectories'
- New function 'Get-WindowsEnvironmentVariables'
- New function 'Get-WindowsEnvironmentVariable'
- New function 'Get-WindowsDefaultBrowser'
- New function 'Get-UniqueNameIfDuplicate'
- New function 'Get-RandomAlphanumericString'
- New function 'Get-PythonVENVDetails'
- New function 'Get-PythonInstallations'
- New function 'Get-NVMVersion'
- New function 'Get-NVMNodeNPMVersions'
- New function 'Get-NVMNodeInstallationExe'
- New function 'Get-NVMNodeInstallationDirectory'
- New function 'Get-NVMLatestNodeVersionInstalled'
- New function 'Get-NumberOfProcessorCoresAndThreads'
- New function 'Get-NodeInstalledVersions'
- New function 'Get-NodeInstalledVersionDetails'
- New function 'Get-NodeInstalledNPMVersion'
- New function 'Get-NodeGlobalModules'
- New function 'Get-ModulePrivateFunctions'
- New function 'Get-MinicondaInstallDetails'
- New function 'Get-InstalledPSCoreVersion'
- New function 'Get-FirstUniqueFileByDepth'
- New function 'Get-Enum'
- New function 'Get-ConsoleWidth'
- New function 'Get-CommandSVGO'
- New function 'Get-CommandRSVGConvert'
- New function 'Get-CommandPrettierNext'
- New function 'Get-CommandNVM'
- New function 'Get-CommandNPM'
- New function 'Get-CommandNode'
- New function 'Get-CommandJSXER'
- New function 'Get-CommandInkscape'
- New function 'Get-CommandImageMagick'
- New function 'Get-CommandFonts2SVG'
- New function 'Get-ANSIColorEscapeFromHex'
- New function 'Get-AllDriveInfo'
- New function 'Remove-DiacriticsFromString'
- New function 'Remove-UnusualSymbolsFromString'
- New function 'Format-String'
- New function 'Format-ObjectSortNumerical'
- New function 'Format-NaturalSort'
- New function 'Format-Milliseconds'
- New function 'Format-FileSizeAuto'
- New function 'Format-FileSize'
- New function 'Format-Bytes'
- New function 'Find-SeparatorInList'
- New function 'Expand-ArchivesInDirectory'
- New function 'Copy-WindowsPathsToClipboard'
- New function 'Copy-WindowsDirectoryStructure'
- New function 'ConvertTo-UnescapedRegistryStrings'
- New function 'ConvertTo-RegSZUnescaped'
- New function 'ConvertTo-RegSZEscaped'
- New function 'ConvertTo-FlatObjectSlav'
- New function 'ConvertTo-FlatObjectCookie'
- New function 'ConvertTo-FlatObjectAzure'
- New function 'ConvertTo-FlatDirectory'
- New function 'ConvertTo-FlatArray'
- New function 'ConvertFrom-JSXBINToJSX'
- New function 'ConvertFrom-HashtableToPSObject'
- New function 'Convert-WindowsGUIDToPID'
- New function 'Convert-VariableFontToStaticFonts'
- New function 'Convert-ToPercentage'
- New function 'Convert-SymbolicLinksToFiles'
- New function 'Convert-SVGCropWithInkscape'
- New function 'Convert-PlaintextListToPowershellArray'
- New function 'Convert-OptimizeSVGsWithSVGO'
- New function 'Convert-JsonKeysToLines'
- New function 'Convert-JsonKeysToCommaSeparatedString'
- New function 'Convert-iTermColorsToINI'
- New function 'Convert-FontWOFFDecompress'
- New function 'Convert-FontWOFFCompressGoogle'
- New function 'Convert-FontWOFFCompress'
- New function 'Convert-FontTTFToOTF'
- New function 'Convert-FontToTTXXML'
- New function 'Convert-FontToSVG'
- New function 'Convert-FontOTFToTTF'
- New function 'Convert-FontGlyphsToSVGs'
- New function 'Convert-CommaSeparatedListToPlaintextTable'
- New function 'Convert-ColorRGBToHSV'
- New function 'Convert-ColorRGBToHex'
- New function 'Convert-ColorHSLToRGB'
- New function 'Convert-ColorHexToRGB'
- New function 'Convert-ColorHexToANSICode'
- New function 'Convert-AudioToStemsWithDEMUCS'
- New function 'Confirm-WindowsPathIsProtected'
- New function 'Confirm-PythonPyPiPackageExists'
- New function 'Confirm-PythonFolderIsVENV'
- New function 'Confirm-PathIsSingleFile'
- New function 'Test-InvalidFilenameCharacters'
- New function 'Confirm-PathIsAFile'
- New function 'Confirm-NVMForWindowsIsInstalled'
- New function 'Confirm-NVMForWindowsContainsInstallations'
- New function 'Confirm-NPMPackageExistsInRegistry'
- New function 'Confirm-FolderIsGithubRepo'
- New function 'Add-StringSuffixToFile'
- New function 'Add-NumericSuffixToFile'
- Initial release

