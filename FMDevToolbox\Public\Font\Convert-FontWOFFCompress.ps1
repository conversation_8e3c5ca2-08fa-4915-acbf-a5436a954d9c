using namespace System.Collections.Generic

function Convert-FontWOFFCompress {
    [CmdletBinding()]
    param (
        [Parameter(
            Mandatory,
            Position = 0,
            ValueFromPipeline,
            ValueFromPipelineByPropertyName
        )]
        [ValidateScript({
            # Throw on invalid path
            if(-not(Test-Path -Path $_ -IsValid)){
                throw "The directory/file name '$_' is not valid."
            }
            # Throw on wildcard characters
            if($_ -match '[\?\*]'){
                throw "Wildcard characters *, ? are not acceptable."
            }
            $true
        })]
        [Alias('PSPath')]
        [ValidateNotNullOrEmpty()]
        [String[]] $LiteralPath,

        [Parameter(ValueFromPipelineByPropertyName)]
        [ValidateSet('woff2','woff','both')]
        [String] $WoffFormat = "woff2",

        [Parameter(ValueFromPipelineByPropertyName)]
        [ValidateRange(4,32)]
        [Int32] $MaxThreads = 24
    )

    begin {
        $cmdFtcli = Initialize-FontFoundryToolsVENV -FontToolsVersion FontFoundryTools
        $fontList = [HashSet[String]]@()
    }
    process {

        $resolvedPaths = [HashSet[String]]@()
        foreach ($curPath in $LiteralPath) {
            if (!(Test-Path -LiteralPath $curPath)) {
                $ex = [ItemNotFoundException]::new("Cannot find path '$curPath' because it does not exist.")
                $category = [ErrorCategory]::ObjectNotFound
                $errRecord = [ErrorRecord]::new($ex,'PathNotFound',$category,$curPath)
                $psCmdlet.WriteError($errRecord)
                continue
            }
            # Resolve any relative paths
            $null = $resolvedPaths.Add($psCmdlet.SessionState.Path.GetUnresolvedProviderPathFromPSPath($curPath))
        }
        foreach ($path in $resolvedPaths) {
            $item = Get-Item -LiteralPath $path -Force
            if (-not $item.PSIsContainer) {
                if($item.Extension -in @('.ttf','.otf')){
                    $null = $fontList.Add($item.FullName)
                }
            }
        }
    }

    end {
        $fontList | % -Parallel {
            $curFont = $_
            $cmdFtcli = $Using:cmdFtcli
            $WoffFormat = $Using:WoffFormat

            if($WoffFormat -eq 'both'){
                $FtcliParams1 = "converter", "ft2wf", "--flavor", 'woff', "--no-overwrite", $curFont
                $FtcliParams2 = "converter", "ft2wf", "--flavor", 'woff2', "--no-overwrite", $curFont
                & $cmdFtcli $FtcliParams1
                & $cmdFtcli $FtcliParams2
            }
            else {
                $FtcliParams = "converter", "ft2wf", "--flavor", $WoffFormat, "--no-overwrite", "$curFont"
                & $cmdFtcli $FtcliParams
            }
        } -ThrottleLimit $MaxThreads

        & deactivate

        Write-Host -f White "➔ All fonts have been successfully compressed to WOFF files."
    }
}