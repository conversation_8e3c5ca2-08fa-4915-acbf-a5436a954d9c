﻿using namespace System.IO
using namespace System.Collections.Generic
using namespace System.Management.Automation

function Get-ModuleConfiguration {
    [OutputType([System.Collections.Hashtable])]
    [CmdletBinding()]
    param (
        [Parameter(ParameterSetName="Python")]
        [Switch] $Python
    )

    $Config = Get-Content -Path "$PSScriptRoot\..\..\Config\Configuration.json" | ConvertFrom-Json

    if($Python){
        $PythonConfigObject = @{
            PythonVENVRoot                = $Config.Python.PythonVENVRoot
            PythonFontToolsVENV           = $Config.Python.PythonFontToolsVENV
            PythonFontToolsVENVFtcli      = $Config.Python.PythonFontToolsVENVFtcli
            PythonFontToolsVENVActivate   = $Config.Python.PythonFontToolsVENVActivate
            PythonFontToolsNGVENV         = $Config.Python.PythonFontToolsNGVENV
            PythonFontToolsNGVENVFtcli    = $Config.Python.PythonFontToolsNGVENVFtcli
            PythonFontToolsNGVENVActivate = $Config.Python.PythonFontToolsNGVENVActivate
        }
        return $PythonConfigObject
    }
}