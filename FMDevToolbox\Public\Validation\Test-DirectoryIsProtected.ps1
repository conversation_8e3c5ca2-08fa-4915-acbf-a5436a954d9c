﻿using namespace System.IO
using namespace System.Collections.Generic
using namespace System.Management.Automation

function Test-DirectoryIsProtected {
    [OutputType([bool])]
    [CmdletBinding()]
    param(
        [Parameter(
            Mandatory,
            Position = 0,
            ValueFromPipeline,
            ValueFromPipelineByPropertyName,
            HelpMessage = "Literal path to one or more locations to process."
        )]
        [ValidateScript({
            # Throw on invalid path
            if(-not(Test-Path -Path $_ -IsValid)){ throw "The directory/file name '$_' is not valid." }
            # Throw on wildcard characters
            if($_ -match '[\?\*]'){
                throw "Wildcard characters *, ? are not acceptable."
            }
            $true
        })]
        [Alias('PSPath')]
        [ValidateNotNullOrEmpty()]
        [String[]] $LiteralPath
    )

    begin {

        $VAR_OS_DRIVE         = $env:SystemDrive
        $VAR_USER_HOME        = [Environment]::GetFolderPath([Environment+SpecialFolder]::UserProfile)
        $VAR_USER_DESKTOP     = [Environment]::GetFolderPath([Environment+SpecialFolder]::Desktop)
        $VAR_SYS_USERS_DIR    = [System.IO.Path]::Combine($VAR_OS_DRIVE, 'Users')

        $UNSAFE_STATIC = @{
            STATIC_USER_HOME    = $VAR_USER_HOME
            STATIC_USER_DESKTOP = $VAR_USER_DESKTOP
            STATIC_USERS_DIR    = $VAR_SYS_USERS_DIR
        }

        $UNSAFE_RECURSIVE = @{
            RECURSE_SYS_WINDOWS_DIR        = [Environment]::GetFolderPath([Environment+SpecialFolder]::Windows)
            RECURSE_SYS_COMMON_FILES64     = [Environment]::GetFolderPath([Environment+SpecialFolder]::CommonProgramFiles)
            RECURSE_SYS_COMMON_FILES86     = [Environment]::GetFolderPath([Environment+SpecialFolder]::CommonProgramFilesX86)
            RECURSE_SYS_APPDATA            = Join-Path $VAR_USER_HOME 'AppData'
            RECURSE_SYS_PROGRAM_FILES86    = [Environment]::GetFolderPath([Environment+SpecialFolder]::ProgramFilesX86)
            RECURSE_SYS_PROGRAM_FILES64    = [Environment]::GetFolderPath([Environment+SpecialFolder]::ProgramFiles)
            RECURSE_SYS_PROGRAM_DATA       = [Environment]::GetFolderPath([Environment+SpecialFolder]::CommonApplicationData)
            RECURSE_USER_PUBLIC_ROOT       = Join-Path $VAR_OS_DRIVE 'Users\Public'
            RECURSE_USER_DEFAULT_ROOT      = Join-Path $VAR_OS_DRIVE 'Users\Default'
            RECURSE_OS_RECYCLEBIN          = Join-Path $VAR_OS_DRIVE '$Recycle.Bin'
            RECURSE_OS_WINREAGENT          = Join-Path $VAR_OS_DRIVE '$WinREAgent'
            RECURSE_OS_RECOVERY            = Join-Path $VAR_OS_DRIVE 'Recovery'
            RECURSE_OS_SYS_VOLUME          = Join-Path $VAR_OS_DRIVE 'System Volume Information'
            RECURSE_OS_MINICONDA           = Join-Path $VAR_OS_DRIVE 'Python\miniconda3'
        }

        $VAR_OS_DRIVE_ESCAPED = [regex]::Escape($VAR_OS_DRIVE)
        $VAR_USER_HOME_ESCAPED = [regex]::Escape($VAR_USER_HOME)

        $UNSAFE_RECURSIVE_REGEX = @{
            RECURSE_REGEX_PYTHON1       = "$VAR_OS_DRIVE_ESCAPED\\Python\\Python[\d]{2,4}\\?$"
            RECURSE_REGEX_PYTHON2       = "$VAR_USER_HOME_ESCAPED\\Python[\d]{2,4}\\?$"
            RECURSE_REGEX_MINICONDA1    = "$VAR_OS_DRIVE_ESCAPED\\Python\\Miniconda[\d]{1,}\\?$"
            RECURSE_REGEX_MINICONDA2    = "$VAR_USER_HOME_ESCAPED\\Miniconda[\d]{1,}\\?$"
        }

        $UNSAFE_STATIC_PERSONAL = @{
            STATIC_BIN            = $env:FM_BIN
            STATIC_WRAPPERS       = $env:FM_PS_WRAPPERS
            STATIC_PYTHON_VENVS   = $env:FM_PY_VENV
            STATIC_TOOLS          = $env:FM_TOOLS
        }
    }

    process {

        :outer foreach ($Directory in $LiteralPath) {
            if(-not(Test-Path $Directory -PathType Container)){
                continue
            }

            $Directory = $Directory.TrimEnd([System.IO.Path]::DirectorySeparatorChar)

            foreach ($UNSAFE_DIR in $UNSAFE_STATIC.GetEnumerator()) {
                if ($Directory -eq $UNSAFE_DIR.Value) { $true; continue outer}
            }
            foreach ($UNSAFE_DIR in $UNSAFE_STATIC_PERSONAL.GetEnumerator()) {
                if ($Directory -eq $UNSAFE_DIR.Value) { $true; continue outer}
            }
            foreach ($UNSAFE_DIR in $UNSAFE_RECURSIVE.GetEnumerator()) {
                if ($Directory -like "$($UNSAFE_DIR.Value)*") { $true; continue outer}
            }
            foreach ($UNSAFE_DIR in $UNSAFE_RECURSIVE_REGEX.GetEnumerator()) {
                if ($Directory -match $UNSAFE_DIR.Value) { $true; continue outer}
            }

            $Directory -match '^[a-zA-Z]:\\?(?:System Volume Information|\$RECYCLE\.BIN)?$'
        }
    }

    end {}
}