﻿using namespace System.Collections.Generic
using namespace System.IO

function Initialize-FontFoundryToolsVENV {
    <#
    .SYNOPSIS
    Initializes the virtual environment for Font Foundry Tools.

    .DESCRIPTION
    The Initialize-FontFoundryToolsVENV function activates a Python virtual environment
    for the specified version of Font Foundry Tools. It checks for the existence of
    required paths and returns the command object for the ftcli executable.

    .PARAMETER FontToolsVersion
    Specifies the version of Font Foundry Tools to initialize. Valid values are
    'FontFoundryTools' and 'FontFoundryToolsNG'. Defaults to 'FontFoundryTools'.

    .EXAMPLE
    # **Example 1**
    # This example demonstrates how to initialize the default FontFoundryTools environment.
    Initialize-FontFoundryToolsVENV

    .EXAMPLE
    # **Example 2**
    # This example demonstrates how to initialize the FontFoundryToolsNG environment.
    Initialize-FontFoundryToolsVENV -FontToolsVersion 'FontFoundryToolsNG'

    .EXAMPLE
    # **Example 3**
    # This example demonstrates handling an error when the virtual environment is missing.
    try {
        Initialize-FontFoundryToolsVENV -FontToolsVersion 'FontFoundryTools'
    } catch {
        Write-Error "Initialization failed: $_"
    }

    .EXAMPLE
    # **Example 4**
    # This example demonstrates using the function in a script to ensure the environment is ready.
    if (Initialize-FontFoundryToolsVENV -FontToolsVersion 'FontFoundryToolsNG') {
        Write-Host "Environment initialized successfully."
    }

    .EXAMPLE
    # **Example 5**
    # This example demonstrates initializing the environment and executing a command from it.
    $cmdFtcli = Initialize-FontFoundryToolsVENV
    & $cmdFtcli --version

    .EXAMPLE
    # **Example 6**
    # This example demonstrates checking for the presence of the ftcli command after initialization.
    $cmdFtcli = Initialize-FontFoundryToolsVENV -FontToolsVersion 'FontFoundryToolsNG'
    if ($cmdFtcli) {
        Write-Host "ftcli command is available."
    }

    .OUTPUTS
    System.Management.Automation.ApplicationInfo
    Returns the command object for the ftcli executable if successful.

    .NOTES
    Author: Futuremotion
    Website: https://github.com/futuremotiondev
    Date: 06-14-2025
    #>
    [CmdletBinding()]
    param (
        [ValidateSet('FontFoundryTools','FontFoundryToolsNG')]
        [String] $FontToolsVersion = 'FontFoundryTools'
    )
    # Define configurations in a hashtable
    $configurations = @{
        'FontFoundryTools'   = @{
            Venv      = $script:PythonFontToolsVENV
            Activate  = $script:PythonFontToolsVENVActivate
            Ftcli     = $script:PythonFontToolsVENVFtcli
        }
        'FontFoundryToolsNG' = @{
            Venv      = $script:PythonFontToolsNGVENV
            Activate  = $script:PythonFontToolsNGVENVActivate
            Ftcli     = $script:PythonFontToolsNGVENVFtcli
        }
    }
    $config = $configurations[$FontToolsVersion]
    $ftcliFilename = [Path]::GetFileName($config.Ftcli)

    # Validate paths using a loop
    foreach ($path in @($config.Venv, $config.Activate, $config.Ftcli)) {
        if (-not (Test-Path -LiteralPath $path)) {
            throw "Required file is missing or not installed. ($path)"
        }
    }

    & $config.Activate

    try {
        $cmdFtcli = Get-Command $ftcliFilename -CommandType Application
    } catch {
        throw "$ftcliFilename cannot be located in $config.Ftcli"
    }

    return $cmdFtcli
}