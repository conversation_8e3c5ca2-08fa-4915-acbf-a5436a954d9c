{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"Futuremotion.FMDevToolbox/1.0.0": {"dependencies": {"System.Management.Automation": "7.2.24"}, "runtime": {"Futuremotion.FMDevToolbox.dll": {}}}, "Microsoft.ApplicationInsights/2.21.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "5.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {"assemblyVersion": "2.21.0.429", "fileVersion": "2.21.0.429"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Management.Infrastructure/2.0.0": {"dependencies": {"Microsoft.Management.Infrastructure.Runtime.Unix": "2.0.0", "Microsoft.Management.Infrastructure.Runtime.Win": "2.0.0"}}, "Microsoft.Management.Infrastructure.Runtime.Unix/2.0.0": {"runtimeTargets": {"runtimes/unix/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "2.0.0.0", "fileVersion": "2.0.0.0"}}}, "Microsoft.Management.Infrastructure.Runtime.Win/2.0.0": {"runtimeTargets": {"runtimes/win-arm/lib/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {"rid": "win-arm", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.18362.1"}, "runtimes/win-arm/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "win-arm", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.18362.1"}, "runtimes/win-arm64/lib/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {"rid": "win-arm64", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.18362.1"}, "runtimes/win-arm64/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "win-arm64", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.18362.1"}, "runtimes/win10-x64/lib/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {"rid": "win10-x64", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.18362.1"}, "runtimes/win10-x64/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "win10-x64", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.18362.1"}, "runtimes/win10-x86/lib/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {"rid": "win10-x86", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.18362.1"}, "runtimes/win10-x86/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "win10-x86", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.18362.1"}, "runtimes/win7-x64/lib/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {"rid": "win7-x64", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.18362.1"}, "runtimes/win7-x64/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "win7-x64", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.18362.1"}, "runtimes/win7-x86/lib/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {"rid": "win7-x86", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.18362.1"}, "runtimes/win7-x86/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "win7-x86", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.18362.1"}, "runtimes/win8-x64/lib/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {"rid": "win8-x64", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.18362.1"}, "runtimes/win8-x64/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "win8-x64", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.18362.1"}, "runtimes/win8-x86/lib/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {"rid": "win8-x86", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.18362.1"}, "runtimes/win8-x86/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "win8-x86", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.18362.1"}, "runtimes/win81-x64/lib/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {"rid": "win81-x64", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.18362.1"}, "runtimes/win81-x64/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "win81-x64", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.18362.1"}, "runtimes/win81-x86/lib/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {"rid": "win81-x86", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.18362.1"}, "runtimes/win81-x86/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"rid": "win81-x86", "assetType": "runtime", "assemblyVersion": "1.0.0.0", "fileVersion": "10.0.18362.1"}, "runtimes/win-arm/native/Microsoft.Management.Infrastructure.Native.Unmanaged.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "10.0.18362.1"}, "runtimes/win-arm/native/mi.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "10.0.18362.1"}, "runtimes/win-arm/native/miutils.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "10.0.18362.1"}, "runtimes/win-arm64/native/Microsoft.Management.Infrastructure.Native.Unmanaged.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "10.0.18362.1"}, "runtimes/win-arm64/native/mi.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "10.0.18362.1"}, "runtimes/win-arm64/native/miutils.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "10.0.18362.1"}, "runtimes/win10-x64/native/Microsoft.Management.Infrastructure.Native.Unmanaged.dll": {"rid": "win10-x64", "assetType": "native", "fileVersion": "10.0.18362.1"}, "runtimes/win10-x64/native/mi.dll": {"rid": "win10-x64", "assetType": "native", "fileVersion": "10.0.18362.1"}, "runtimes/win10-x64/native/miutils.dll": {"rid": "win10-x64", "assetType": "native", "fileVersion": "10.0.18362.1"}, "runtimes/win10-x86/native/Microsoft.Management.Infrastructure.Native.Unmanaged.dll": {"rid": "win10-x86", "assetType": "native", "fileVersion": "10.0.18362.1"}, "runtimes/win10-x86/native/mi.dll": {"rid": "win10-x86", "assetType": "native", "fileVersion": "10.0.18362.1"}, "runtimes/win10-x86/native/miutils.dll": {"rid": "win10-x86", "assetType": "native", "fileVersion": "10.0.18362.1"}, "runtimes/win7-x64/native/Microsoft.Management.Infrastructure.Native.Unmanaged.dll": {"rid": "win7-x64", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win7-x64/native/mi.dll": {"rid": "win7-x64", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win7-x64/native/miutils.dll": {"rid": "win7-x64", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win7-x86/native/Microsoft.Management.Infrastructure.Native.Unmanaged.dll": {"rid": "win7-x86", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win7-x86/native/mi.dll": {"rid": "win7-x86", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win7-x86/native/miutils.dll": {"rid": "win7-x86", "assetType": "native", "fileVersion": "10.0.14394.1000"}, "runtimes/win8-x64/native/mi.dll": {"rid": "win8-x64", "assetType": "native", "fileVersion": "6.2.9200.22812"}, "runtimes/win8-x64/native/miutils.dll": {"rid": "win8-x64", "assetType": "native", "fileVersion": "6.2.9200.22812"}, "runtimes/win8-x86/native/mi.dll": {"rid": "win8-x86", "assetType": "native", "fileVersion": "6.2.9200.22812"}, "runtimes/win8-x86/native/miutils.dll": {"rid": "win8-x86", "assetType": "native", "fileVersion": "6.2.9200.22812"}, "runtimes/win81-x64/native/Microsoft.Management.Infrastructure.Native.Unmanaged.dll": {"rid": "win81-x64", "assetType": "native", "fileVersion": "6.3.9600.16384"}, "runtimes/win81-x64/native/mi.dll": {"rid": "win81-x64", "assetType": "native", "fileVersion": "6.3.9600.16384"}, "runtimes/win81-x64/native/miutils.dll": {"rid": "win81-x64", "assetType": "native", "fileVersion": "6.3.9600.16384"}, "runtimes/win81-x86/native/Microsoft.Management.Infrastructure.Native.Unmanaged.dll": {"rid": "win81-x86", "assetType": "native", "fileVersion": "6.3.9600.16384"}, "runtimes/win81-x86/native/mi.dll": {"rid": "win81-x86", "assetType": "native", "fileVersion": "6.3.9600.16384"}, "runtimes/win81-x86/native/miutils.dll": {"rid": "win81-x86", "assetType": "native", "fileVersion": "6.3.9600.16384"}}}, "Microsoft.PowerShell.CoreCLR.Eventing/7.2.24": {"dependencies": {"System.Diagnostics.EventLog": "6.0.0"}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.PowerShell.CoreCLR.Eventing.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.2.24.500", "fileVersion": "7.2.24.500"}}}, "Microsoft.PowerShell.Native/7.2.1": {"runtimeTargets": {"runtimes/linux-arm/native/libpsl-native.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libpsl-native.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libpsl-native.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libmi.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libpsl-native.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libpsrpclient.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libmi.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libpsl-native.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libpsrpclient.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/PowerShell.Core.Instrumentation.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-arm/native/pwrshplugin.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-arm64/native/PowerShell.Core.Instrumentation.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-arm64/native/pwrshplugin.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-x64/native/PowerShell.Core.Instrumentation.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-x64/native/pwrshplugin.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-x86/native/PowerShell.Core.Instrumentation.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "10.0.10011.16384"}, "runtimes/win-x86/native/pwrshplugin.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "10.0.10011.16384"}}}, "Microsoft.Win32.Registry.AccessControl/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.1"}, "runtime": {"lib/net6.0/Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.Registry.AccessControl.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "System.CodeDom/6.0.0": {"runtime": {"lib/net6.0/System.CodeDom.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Configuration.ConfigurationManager/6.0.1": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.922.41905"}}}, "System.Diagnostics.DiagnosticSource/5.0.1": {}, "System.Diagnostics.EventLog/6.0.0": {"runtime": {"lib/net6.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.DirectoryServices/6.0.1": {"dependencies": {"System.Security.AccessControl": "6.0.1", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.DirectoryServices.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "6.0.1423.7309"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.DirectoryServices.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.0.0", "fileVersion": "6.0.1423.7309"}}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Formats.Asn1/6.0.1": {"runtime": {"lib/net6.0/System.Formats.Asn1.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.3224.31407"}}}, "System.Management/6.0.2": {"dependencies": {"System.CodeDom": "6.0.0"}, "runtime": {"lib/net6.0/System.Management.dll": {"assemblyVersion": "6.0.0.2", "fileVersion": "6.0.1823.26907"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.2", "fileVersion": "6.0.1823.26907"}}}, "System.Management.Automation/7.2.24": {"dependencies": {"Microsoft.ApplicationInsights": "2.21.0", "Microsoft.CSharp": "4.7.0", "Microsoft.Management.Infrastructure": "2.0.0", "Microsoft.PowerShell.CoreCLR.Eventing": "7.2.24", "Microsoft.PowerShell.Native": "7.2.1", "Microsoft.Win32.Registry.AccessControl": "6.0.0", "Newtonsoft.Json": "13.0.3", "System.Configuration.ConfigurationManager": "6.0.1", "System.Diagnostics.DiagnosticSource": "5.0.1", "System.DirectoryServices": "6.0.1", "System.Formats.Asn1": "6.0.1", "System.Management": "6.0.2", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Security.AccessControl": "6.0.1", "System.Security.Cryptography.Pkcs": "6.0.4", "System.Security.Permissions": "6.0.0", "System.Text.Encoding.CodePages": "6.0.0"}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Management.Automation.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "7.2.24.500", "fileVersion": "7.2.24.500"}, "runtimes/win/lib/net6.0/System.Management.Automation.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.2.24.500", "fileVersion": "7.2.24.500"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.AccessControl/6.0.1": {"runtime": {"lib/net6.0/System.Security.AccessControl.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.2924.17105"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.AccessControl.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.2924.17105"}}}, "System.Security.Cryptography.Pkcs/6.0.4": {"dependencies": {"System.Formats.Asn1": "6.0.1"}, "runtime": {"lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.1923.31806"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.1923.31806"}}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.1", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}}}, "libraries": {"Futuremotion.FMDevToolbox/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.ApplicationInsights/2.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-btZEDWAFNo9CoYliMCriSMTX3ruRGZTtYw4mo2XyyfLlowFicYVM2Xszi5evDG95QRYV7MbbH3D2RqVwfZlJHw==", "path": "microsoft.applicationinsights/2.21.0", "hashPath": "microsoft.applicationinsights.2.21.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Management.Infrastructure/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IaKZRNBBv3sdrmBWd+aqwHq8cVHk/3WgWFAN/dt40MRY9rbtHiDfTTmaEN0tGTmQqGCGDo/ncntA8MvFMvcsRw==", "path": "microsoft.management.infrastructure/2.0.0", "hashPath": "microsoft.management.infrastructure.2.0.0.nupkg.sha512"}, "Microsoft.Management.Infrastructure.Runtime.Unix/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-p0lslMX5bdWLxO2P7ao+rjAMOB0LEwPYpzvdCQ2OEYgX2NxFpQ8ILvqPGnYlTAb53rT8gu5DyIol1HboHFYfxQ==", "path": "microsoft.management.infrastructure.runtime.unix/2.0.0", "hashPath": "microsoft.management.infrastructure.runtime.unix.2.0.0.nupkg.sha512"}, "Microsoft.Management.Infrastructure.Runtime.Win/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-vjBWQeDOjgernkrOdbEgn7M70SF7hof7ORdKPSlL06Uc15+oYdth5dZju9KsgUoti/cwnkZTiwtDx/lRtay0sA==", "path": "microsoft.management.infrastructure.runtime.win/2.0.0", "hashPath": "microsoft.management.infrastructure.runtime.win.2.0.0.nupkg.sha512"}, "Microsoft.PowerShell.CoreCLR.Eventing/7.2.24": {"type": "package", "serviceable": true, "sha512": "sha512-WCjyZsWP0KsidaYSo6hAKc1uh9YpQaPUXWCtBgU73uwRA22ivKA4pzd4+9V2nb7EbfkOdetLDXtmf+5CUUdLfA==", "path": "microsoft.powershell.coreclr.eventing/7.2.24", "hashPath": "microsoft.powershell.coreclr.eventing.7.2.24.nupkg.sha512"}, "Microsoft.PowerShell.Native/7.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ce7sccSKHemYA/p/ADD3twqp2RgvtPV6ch+hY6n50tWkGmytfSccYgnhtG30/1SaU0ktCLvg0/NSE6XB10XFqA==", "path": "microsoft.powershell.native/7.2.1", "hashPath": "microsoft.powershell.native.7.2.1.nupkg.sha512"}, "Microsoft.Win32.Registry.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UoE+eeuBKL+GFHxHV3FjHlY5K8Wr/IR7Ee/a2oDNqFodF1iMqyt5hIs0U9Z217AbWrHrNle4750kD03hv1IMZw==", "path": "microsoft.win32.registry.accesscontrol/6.0.0", "hashPath": "microsoft.win32.registry.accesscontrol.6.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "System.CodeDom/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA==", "path": "system.codedom/6.0.0", "hashPath": "system.codedom.6.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-jXw9MlUu/kRfEU0WyTptAVueupqIeE3/rl0EZDMlf8pcvJnitQ8HeVEp69rZdaStXwTV72boi/Bhw8lOeO+U2w==", "path": "system.configuration.configurationmanager/6.0.1", "hashPath": "system.configuration.configurationmanager.6.0.1.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uXQEYqav2V3zP6OwkOKtLv+qIi6z3m1hsGyKwXX7ZA7htT4shoVccGxnJ9kVRFPNAsi1ArZTq2oh7WOto6GbkQ==", "path": "system.diagnostics.diagnosticsource/5.0.1", "hashPath": "system.diagnostics.diagnosticsource.5.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lcyUiXTsETK2ALsZrX+nWuHSIQeazhqPphLfaRxzdGaG93+0kELqpgEHtwWOlQe7+jSFnKwaCAgL4kjeZCQJnw==", "path": "system.diagnostics.eventlog/6.0.0", "hashPath": "system.diagnostics.eventlog.6.0.0.nupkg.sha512"}, "System.DirectoryServices/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-935IbO7h5FDGYxeO3cbx/CuvBBuk/VI8sENlfmXlh1pupNOB3LAGzdYdPY8CawGJFP7KNrHK5eUlsFoz3F6cuA==", "path": "system.directoryservices/6.0.1", "hashPath": "system.directoryservices.6.0.1.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Formats.Asn1/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-glgtKqWJpH9GDw0m9I5xFiF6WDIQqi/eZXU6MkMRPzAWEERGGAJh+qztkrlWSDbokQ1jalj5NcBNIvVoSDpSSA==", "path": "system.formats.asn1/6.0.1", "hashPath": "system.formats.asn1.6.0.1.nupkg.sha512"}, "System.Management/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-s6c9x2Kghd+ncEDnT6ApYVOacDXr/Y57oSUSx6wjegMOfKxhtrXn3PdASPNU59y3kB9OJ1yb3l5k6uKr3bhqew==", "path": "system.management/6.0.2", "hashPath": "system.management.6.0.2.nupkg.sha512"}, "System.Management.Automation/7.2.24": {"type": "package", "serviceable": true, "sha512": "sha512-8X1cqI1NCz8Oa+20/+34YFBvs5sk/iSG9C4/49/Z6AeIVeecm+3c3VqaQ0tmSyd/DSEOWxEwLUJpOML/l50C7w==", "path": "system.management.automation/7.2.24", "hashPath": "system.management.automation.7.2.24.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-IQ4NXP/B3Ayzvw0rDQzVTYsCKyy0Jp9KI6aYcK7UnGVlR9+Awz++TIPCQtPYfLJfOpm8ajowMR09V7quD3sEHw==", "path": "system.security.accesscontrol/6.0.1", "hashPath": "system.security.accesscontrol.6.0.1.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/6.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-LGbXi1oUJ9QgCNGXRO9ndzBL/GZgANcsURpMhNR8uO+rca47SZmciS3RSQUvlQRwK3QHZSHNOXzoMUASKA+Anw==", "path": "system.security.cryptography.pkcs/6.0.4", "hashPath": "system.security.cryptography.pkcs.6.0.4.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}}}