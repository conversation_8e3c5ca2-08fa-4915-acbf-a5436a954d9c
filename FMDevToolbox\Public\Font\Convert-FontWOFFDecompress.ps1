function Convert-FontWOFFDecompress {
    [CmdletBinding()]
    param (
        [Parameter(
            Mandatory,
            Position = 0,
            ValueFromPipeline,
            ValueFromPipelineByPropertyName,
            HelpMessage = "Literal path to one or more locations to process."
        )]
        [ValidateScript({
            # Throw on invalid path
            if(-not(Test-Path -Path $_ -IsValid)){ throw "The directory/file name '$_' is not valid." }
            # Throw on wildcard characters
            if($_ -match '[\?\*]'){
                throw "Wildcard characters *, ? are not acceptable."
            }
            $true
        })]
        [Alias('PSPath')]
        [ValidateNotNullOrEmpty()]
        [String[]] $LiteralPath,

        [ValidateRange(4,32)]
        [Int32] $MaxThreads = 24

    )

    begin {
        $cmdFtcli = Initialize-FontFoundryToolsVENV
        $woffList = [HashSet[String]]@()
    }

    process {

        $resolvedPaths = [HashSet[String]]@()
        foreach ($curPath in $LiteralPath) {
            if (!(Test-Path -LiteralPath $curPath)) {
                $ex = [ItemNotFoundException]::new("Cannot find path '$curPath' because it does not exist.")
                $category = [ErrorCategory]::ObjectNotFound
                $errRecord = [ErrorRecord]::new($ex,'PathNotFound',$category,$curPath)
                $psCmdlet.WriteError($errRecord)
                continue
            }
            # Resolve any relative paths
            $null = $resolvedPaths.Add($psCmdlet.SessionState.Path.GetUnresolvedProviderPathFromPSPath($curPath))
        }
        foreach ($path in $resolvedPaths) {
            $item = Get-Item -LiteralPath $path -Force
            if (-not $item.PSIsContainer) {
                if($item.Extension -in @('.woff','.woff2')){
                    $null = $woffList.Add($item.FullName)
                }
            }
        }
    }

    end {

        $woffList | % -Parallel {
            $curFont = $_
            $cmdFtcli = $Using:cmdFtcli
            $FtcliParams = "converter", "wf2ft", "--no-overwrite", $curFont
            & $cmdFtcli $FtcliParams
        } -ThrottleLimit $MaxThreads

        & deactivate

        Write-Host -f White "➔ All WOFF files have been successfully decompressed."

    }
}