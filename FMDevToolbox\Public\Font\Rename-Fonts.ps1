﻿using namespace System.Collections.Generic
using namespace System.Management.Automation

function Rename-Fonts {
    [CmdletBinding()]
    param (
        [Parameter(
            Mandatory,
            Position = 0,
            ValueFromPipeline,
            ValueFromPipelineByPropertyName
        )]
        [ValidateScript({
            # Throw on invalid path
            if(-not(Test-Path -Path $_ -IsValid)){
                throw "The directory/file name '$_' is not valid."
            }
            # Throw on wildcard characters
            if($_ -match '[\?\*]'){
                throw "Wildcard characters *, ? are not acceptable."
            }
            $true
        })]
        [Alias('PSPath')]
        [ValidateNotNullOrEmpty()]
        [String[]] $LiteralPath,

        [Parameter(ValueFromPipelineByPropertyName)]
        [ValidateSet('Select','FamilyNameStyleName','PostScriptName','FullFontName')]
        [String] $Method = 'Select',

        [Parameter(ValueFromPipelineByPropertyName)]
        [Switch] $Recurse,

        [Parameter(ValueFromPipelineByPropertyName)]
        [ValidateRange(4,64)]
        [Int32] $MaxThreads = 24
    )

    begin {
        $cmdFtcli = Initialize-FontFoundryToolsVENV -FontToolsVersion FontFoundryTools
        $validFontArray = @('.ttf','.otf','.woff','.woff2')
        $fontList = [HashSet[String]]@()
        $dirList = [HashSet[String]]@()
    }

    process {
        $resolvedPaths = [HashSet[String]]@()
        foreach ($curPath in $LiteralPath) {
            if (-not (Test-Path -LiteralPath $curPath)) {
                $ex = [ItemNotFoundException]::new("Cannot find path '$curPath' because it does not exist.")
                $category = [ErrorCategory]::ObjectNotFound
                $errRecord = [ErrorRecord]::new($ex,'PathNotFound',$category,$curPath)
                $psCmdlet.WriteError($errRecord)
                continue
            }
            # Resolve any relative paths
            $null = $resolvedPaths.Add($psCmdlet.SessionState.Path.GetUnresolvedProviderPathFromPSPath($curPath))
        }
        foreach ($path in $resolvedPaths) {
            $item = Get-Item -LiteralPath $path -Force
            if ($item.PSIsContainer) {
                $null = $dirList.Add($item.FullName)
            }
            elseif($item.Extension -in $validFontArray){
                $null = $fontList.Add($item.FullName)
            }
        }
    }

    end {
        $renameFontsHelpUrl = "https://foundrytools-cli.readthedocs.io/en/latest/commands/utils.html#ftcli-utils-font-renamer"
        if($Method -eq 'Select'){
            $methodButtons = [System.Collections.ArrayList]@()
            $null = $methodButtons.Add('FamilyNameStyleName')
            $null = $methodButtons.Add('PostScriptName')
            $null = $methodButtons.Add('FullFontName')
            $methodTaskDialogSplat = @{
                MainInstruction           = "Specify the source string(s) from which to extract the new file names."
                MainContent               = "Fonts can be renamed by FamilyName-StyleName, PostScript Name, or Full Font Name."
                MainButtonStyle           = 'CommandLinks'
                MainIcon                  = 'Information'
                WindowTitle               = 'Font Renaming Method'
                MainButtons               = $methodButtons
                FooterText                = "More information about this renaming process can be found here: <a href=`"$renameFontsHelpUrl`">ftcli utils font-renamer</a>"
                DialogWidth               = 320
                ExpandedInfoOpenByDefault = $true
                AllowCancel               = $true
                Verbose                   = $false
            }
            $RenameFontsResult = Invoke-OokiiTaskDialog @methodTaskDialogSplat
            if(-not $RenameFontsResult){ exit }
            $RenameFontsChosenMethod = switch($RenameFontsResult.Text){
                'FamilyNameStyleName' { '1' }
                'PostScriptName'      { '2' }
                'FullFontName'        { '3' }
            }
        }

        $ftcliFileParams = "utils", "font-renamer", "-s", $RenameFontsChosenMethod
        $ftcliDirParams = "utils", "font-renamer", "-s", $RenameFontsChosenMethod
        if($Recurse) { $ftcliDirParams += "-r" }

        if($fontList.Count -gt 0){

            foreach ($font in $fontList) {
                Write-Verbose "Queued font $font for renaming."
            }

            $fontList | % -Parallel {
                $cmdFtcli = $Using:cmdFtcli
                $ftcliFileParams = $Using:ftcliFileParams
                & $cmdFtcli $ftcliFileParams $_
            } -ThrottleLimit $MaxThreads
        }

        if($dirList.Count -gt 0){

            foreach ($dir in $dirList) {
                Write-Verbose "Queued fonts within directory $dir for renaming."
            }
            if($Recurse){
                Write-Verbose "Recurse was set. All fonts within every passed directory will be renamed recursively."
            }

            $dirList | % -Parallel {
                $cmdFtcli = $Using:cmdFtcli
                $ftcliDirParams = $Using:ftcliDirParams
                & $cmdFtcli $ftcliDirParams $_
            } -ThrottleLimit $MaxThreads
        }

        Write-SpectreHost "[#FFFFFF]➔ Font renaming complete.[/]"

        & deactivate
    }
}

