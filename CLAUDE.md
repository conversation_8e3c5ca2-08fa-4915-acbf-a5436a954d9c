# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

FMDevToolbox is a comprehensive PowerShell 7.1+ module that provides a wide range of utilities for automating development workflows, managing files and fonts, handling Node.js/Python environments, and working with Windows-specific features. The module is organized into public and private functions with a clear structure.

## Development Commands

### Module Building and Testing
- **Build Module**: Run `./FMDevToolbox/Build.ps1` - This handles updating module functions/aliases, rebuilding assemblies, and updating the manifest
- **Import Module**: `Import-Module ./FMDevToolbox/FMDevToolbox.psd1 -Force` - Force reload the module during development
- **Test Module Manifest**: `Test-ModuleManifest -Path ./FMDevToolbox/FMDevToolbox.psd1` - Validate module manifest syntax

### Module Development Functions (in Build.ps1)
- `Update-ModuleFunctionsAndAliases` - Scans Public/Private folders and updates exported functions/aliases
- `Update-FMModule -UpdateFunctionsAndAliases` - Main build function that updates the module manifest

## Architecture and Structure

### Module Organization
- **Public Functions**: `FMDevToolbox/Public/` - Exported functions organized by category (Font/, Image/, File/, Node/, Python/, etc.)
- **Private Functions**: `FMDevToolbox/Private/` - Internal helper functions and loaders
- **Libraries**: `FMDevToolbox/Lib/` - .NET assemblies including custom C# libraries and NuGet packages
- **Data**: `FMDevToolbox/Data/` - Configuration files, templates, and presets for ESLint, Prettier, Git ignores

### Key Dependencies
- **PwshSpectreConsole** (v2.3.0) - For enhanced console UI and formatting
- **Custom .NET Assembly**: `Futuremotion.FMDevToolbox.dll` - Contains Windows-specific utilities
- **External Tools**: Functions expect various CLI tools (Node.js, Python, ImageMagick, Inkscape, SVGO, etc.)

### Module Loading Pattern
The module uses a standard PowerShell pattern:
1. Load assemblies from `Lib/` directory with error handling
2. Dot-source all `.ps1` files from Private and Public directories
3. Export all functions and aliases using `Export-ModuleMember -Function * -Alias *`

### Configuration System
- Module configuration stored in `Config/Configuration.json`
- Script-scoped variables for paths and defaults (Python VENVs, Node.js versions, etc.)
- Environment-specific paths are configurable but default to specific Windows locations

### Function Categories
- **Font Processing**: OTF/TTF conversion, WOFF compression, font analysis, SVG font creation
- **Image Processing**: SVG optimization, format conversion, dimension analysis
- **File Operations**: Bulk operations, symbolic link handling, macOS junk removal
- **Node.js Management**: NVM integration, package management, version switching
- **Python Management**: VENV handling, package installation, FontTools integration
- **Windows Integration**: Explorer integration, registry operations, GUI dialogs
- **Development Tools**: Git operations, build automation, formatting utilities

## Testing and Quality
- Functions should be tested individually since there's no central test framework
- The module manifest validation is critical - always run `Test-ModuleManifest` after changes
- Many functions depend on external CLI tools being available in PATH

## Platform Considerations
- Designed primarily for Windows with PowerShell 7.1+
- Uses Windows-specific assemblies for GUI operations and system integration
- Cross-platform functions exist but Windows features may not work on other platforms