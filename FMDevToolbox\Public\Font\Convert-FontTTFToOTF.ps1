using namespace System.Collections.Generic

function Convert-FontTTFToOTF {
    [CmdletBinding()]
    param (
        [Parameter(
            Mandatory,
            Position = 0,
            ValueFromPipeline,
            ValueFromPipelineByPropertyName,
            HelpMessage = "Literal path to one or more locations to process."
        )]
        [ValidateScript({
            # Throw on invalid path
            if(-not(Test-Path -Path $_ -IsValid)){ throw "The directory/file name '$_' is not valid." }
            # Throw on wildcard characters
            if($_ -match '[\?\*]'){
                throw "Wildcard characters *, ? are not acceptable."
            }
            $true
        })]
        [Alias('PSPath')]
        [ValidateNotNullOrEmpty()]
        [String[]] $LiteralPath,

        [ValidateRange(4,32)]
        [Int32] $MaxThreads = 16
    )
    begin {
        $cmdFtcli = Initialize-FontFoundryToolsVENV -FontToolsVersion FontFoundryTools
        $fontList = [List[String]]@()
    }
    process {

        $resolvedPaths = [HashSet[String]]@()
        foreach ($curPath in $LiteralPath) {
            if (-not (Test-Path -LiteralPath $curPath)) {
                $ex = [ItemNotFoundException]::new("Cannot find path '$curPath' because it does not exist.")
                $category = [ErrorCategory]::ObjectNotFound
                $errRecord = [ErrorRecord]::new($ex,'PathNotFound',$category,$curPath)
                $psCmdlet.WriteError($errRecord)
                continue
            }
            # Resolve any relative paths
            $null = $resolvedPaths.Add($psCmdlet.SessionState.Path.GetUnresolvedProviderPathFromPSPath($curPath))
        }
        foreach ($path in $resolvedPaths) {
            $item = Get-Item -LiteralPath $path -Force
            if (-not $item.PSIsContainer) {
                if($item.Extension -eq '.ttf'){
                    $null = $fontList.Add($item.FullName)
                }
            }
        }
    }

    end {
        $DialogSplat = @{
            MainInstruction = "Please specify the conversion tolerance (0.0-3.0)"
            MainContent     = "Low tolerance adds more points but keeps shapes. High tolerance adds few points but may change shape."
            WindowTitle     = "ftCLI TTF2OTF"
            InputText       = 1
        }
        do {
            $Result = Invoke-OokiiInputDialog @DialogSplat
            if($Result.Result -eq 'Cancel'){ exit }
            [float] $conversionTolerance = $Result.Input
            [Bool] $toleranceIsValid = ($conversionTolerance -ge 0.0 -and $conversionTolerance -le 3.0)
        } while (-not$ToleranceIsValid)

        $fontList | % -Parallel {
            $cmdFtcli = $Using:cmdFtcli
            $tolerance = $Using:conversionTolerance
            $ftcliParams = 'converter', 'ttf2otf', '-t', $tolerance, '--no-overwrite', $_
            & $cmdFtcli $ftcliParams
        } -ThrottleLimit $MaxThreads

        & deactivate

        Write-Host -f White "➔ All TTF fonts have been successfully converted to OTF fonts."
    }
}