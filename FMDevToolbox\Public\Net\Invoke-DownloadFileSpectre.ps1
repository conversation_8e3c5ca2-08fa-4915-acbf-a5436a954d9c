function Invoke-DownloadFileSpectre {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string] $FileURL,

        [Parameter(Mandatory = $true)]
        [string] $DestinationPath,

        [Parameter(Mandatory = $false)]
        [string] $ChangeFileName,

        [Parameter(Mandatory = $false)]
        [switch] $ShowProgress
    )

    begin {
        try {
            # Validate if the destination path exists
            if (!(Test-Path -Path $DestinationPath -PathType Container)) {
                New-Item -Path $DestinationPath -ItemType Directory -Force | Out-Null
            }

            # Validate if the URL is valid
            if (!$FileURL -or !$FileURL -match '^https?://') {
                throw [System.UriFormatException] "Invalid URL format. URL must start with http:// or https://."
            }

            # If progress requested, check SpectreConsole availability
            if ($ShowProgress) {
                if (-not (Get-Module -ListAvailable -Name PwshSpectreConsole)) {
                    throw "PwshSpectreConsole module is required for Spectre progress bar but is not installed."
                }
                Import-Module PwshSpectreConsole -ErrorAction Stop
            }
        }
        catch {
            Write-Error "Validation Error: $_"
            return
        }
    }

    process {
        try {
            # Determine the file name
            if (-not [String]::IsNullOrWhiteSpace($ChangeFileName)) {
                $fileName = $ChangeFileName
            }
            else {
                [string] $urlPath = [System.Web.HttpUtility]::UrlDecode($FileURL)
                $fileName = [System.IO.Path]::GetFileName($urlPath)
            }

            $downloadPath = Join-Path -Path $DestinationPath -ChildPath $fileName
            if (Test-Path -Path $downloadPath) {
                $downloadPath = Get-UniqueNameIfDuplicate -LiteralPath $downloadPath
            }

            # Create an HttpClient instance
            $httpClient = [System.Net.Http.HttpClient]::new()
            $httpClient.Timeout = [timespan]::FromSeconds(20)  # Set timeout to 20 seconds

            # Send the HTTP GET request
            $response = $httpClient.GetAsync($FileURL, [System.Net.Http.HttpCompletionOption]::ResponseHeadersRead).Result

            # Check if the HTTP response was successful
            if (!$response.IsSuccessStatusCode) {
                throw [System.Net.Http.HttpRequestException] "HTTP request failed with status code $($response.StatusCode)"
            }

            # Get the total bytes from the Content-Length header
            $totalBytes = $response.Content.Headers.ContentLength ?? 0

            # Read the content in chunks and write to the file stream
            $buffer = [byte[]]::new(8192)
            $bytesRead = 0
            $stream = $response.Content.ReadAsStreamAsync().Result
            $fileStream = [System.IO.FileStream]::new($downloadPath, [System.IO.FileMode]::Create, [System.IO.FileAccess]::Write)

            if ($ShowProgress) {
                Write-SpectreHost "`n➔ Starting download of [#FFFFFF]$FileURL[/]"
                $fileSize = Format-Bytes -Bytes $totalBytes
                # Use Spectre Console Progress Bar
                Invoke-SpectreCommandWithProgress -ScriptBlock {
                    param (
                        [Spectre.Console.ProgressContext] $Context
                    )
                    $task = $Context.AddTask("Downloading [#FFFFFF]$fileName[/] ($fileSize)")
                    $task.MaxValue = 100
                    while ($true) {
                        $count = $stream.Read($buffer, 0, $buffer.Length)
                        if ($count -le 0) { break }
                        $bytesRead += $count
                        $fileStream.Write($buffer, 0, $count)
                        $percent = [Math]::Ceiling((($bytesRead / $totalBytes) * 100))
                        $task.Value = $percent
                    }
                }
                Write-SpectreHost "[#FFFFFF]Download of $fileName is complete. ([#95F7E0]$downloadPath[/])[/]"
            }
            else {
                # No progress, or no content length available
                while ($true) {
                    $count = $stream.Read($buffer, 0, $buffer.Length)
                    if ($count -le 0) { break }
                    $bytesRead += $count
                    $fileStream.Write($buffer, 0, $count)
                }
            }
            # Close the streams
            $fileStream.Dispose()
            $stream.Dispose()

            $ResultsVariable = [PSCustomObject]@{
                FileURL       = $FileURL
                FileName      = $fileName
                DownloadPath  = $downloadPath
                FileSizeBytes = $totalBytes
                Success       = $true
                Error         = 'None'
            }
            $ResultsVariable
        }
        catch [System.Net.Http.HttpRequestException] {
            Write-Error -Message "Network Error: $_"

            $ResultsVariable = [PSCustomObject]@{
                FileURL       = $FileURL
                FileName      = $fileName
                DownloadPath  = $null
                FileSizeBytes = $null
                Success       = $false
                Error         = "[HttpRequestException] Error: $_"
            }
            $ResultsVariable
        }
        catch [System.IO.IOException] {
            Write-Error -Message "File Error: $_"

            $ResultsVariable = [PSCustomObject]@{
                FileURL       = $FileURL
                FileName      = $fileName
                DownloadPath  = $null
                FileSizeBytes = $null
                Success       = $false
                Error         = "[IOException] Error: $_"
            }
            $ResultsVariable
        }
        catch {
            Write-Error -Message "Unexpected Error: $_"

            $ResultsVariable = [PSCustomObject]@{
                FileURL       = $FileURL
                FileName      = $fileName
                DownloadPath  = $null
                FileSizeBytes = $null
                Success       = $false
                Error         = "Unknown Error: $_"
            }
            $ResultsVariable
        }
    }
}
