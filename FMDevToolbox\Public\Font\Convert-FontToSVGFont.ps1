using namespace System.Collections.Generic
using namespace System.IO

function Convert-FontToSVGFont {
    [CmdletBinding()]
    param (
        [Parameter(
            Mandatory,
            Position = 0,
            ValueFromPipeline,
            ValueFromPipelineByPropertyName
        )]
        [ValidateScript({
            # Throw on invalid path
            if(-not(Test-Path -Path $_ -IsValid)){
                throw "The directory/file name '$_' is not valid."
            }
            # Throw on wildcard characters
            if($_ -match '[\?\*]'){
                throw "Wildcard characters *, ? are not acceptable."
            }
            $true
        })]
        [Alias('PSPath')]
        [ValidateNotNullOrEmpty()]
        [String[]] $LiteralPath
    )

    begin {

        Initialize-NVM
        $cmdOTF2SVG = Get-Command otf2svg.cmd -CommandType Application -EA 0
        if(-not$cmdOTF2SVG){
            Write-Error "otf2svg isn't installed. Install it by running 'npm install -g otf2svg'."
            return
        }
        $cmdTTF2svg = Get-Command ttf2svg.cmd -CommandType Application -EA 0
        if(-not$cmdTTF2svg){
            Write-Error "ttf2svg isn't installed. Install it by running 'npm install @marmooo/ttf2svg -g'."
            return
        }

        $ttfList = [HashSet[string]]@()
        $otfList = [HashSet[string]]@()
        $dirList = [HashSet[string]]@()
    }

    process {

        $resolvedPaths = [HashSet[String]]@()
        foreach ($curPath in $LiteralPath) {
            if (-not (Test-Path -LiteralPath $curPath)) {
                $ex = [ItemNotFoundException]::new("Cannot find path '$curPath' because it does not exist.")
                $category = [ErrorCategory]::ObjectNotFound
                $errRecord = [ErrorRecord]::new($ex,'PathNotFound',$category,$curPath)
                $psCmdlet.WriteError($errRecord)
                continue
            }
            # Resolve any relative paths
            $null = $resolvedPaths.Add($psCmdlet.SessionState.Path.GetUnresolvedProviderPathFromPSPath($curPath))
        }
        foreach ($path in $resolvedPaths) {
            $item = Get-Item -LiteralPath $path -Force
            if ($item.PSIsContainer) {
                $null = $dirList.Add($item.FullName)
            }
            elseif($item.Extension -eq '.otf'){
                $null = $otfList.Add($item.FullName)
            }
            elseif($item.Extension -eq '.ttf'){
                $null = $ttfList.Add($item.FullName)
            }
        }
        if($dirList.Count -gt 0){
            foreach ($curDir in $dirList) {
                Get-ChildItem -LiteralPath $curDir -Force -File -ErrorAction 0 |
                    Where-Object { $_.Extension -eq '.ttf'} | % {
                        $null = $ttfList.Add($_.FullName)
                    }
                Get-ChildItem -LiteralPath $curDir -Force -File -ErrorAction 0 |
                    Where-Object { $_.Extension -eq '.otf'} | % {
                        $null = $otfList.Add($_.FullName)
                }
            }
        }
    }

    end {
        foreach ($ttfFile in $ttfList) {
            $ttfFileSvg = $ttfFile.Substring(0, $ttfFile.LastIndexOf('.')) + '.svg'
            $ttfFileSvgFinal = Get-UniqueNameIfDuplicate -LiteralPath $ttfFileSvg
            $ttf2svgParams = "$ttfFile", '--font', '>', "$ttfFileSvgFinal"
            & $cmdTTF2svg $ttf2svgParams
        }

        foreach ($otfFile in $otfList) {
            $otfFileSvg = $otfFile.Substring(0, $otfFile.LastIndexOf('.')) + '.svg'
            $otfFileSvgFinal = Get-UniqueNameIfDuplicate -LiteralPath $otfFileSvg
            $otf2svgParams = "$otfFile", "$otfFileSvgFinal"
            & $cmdOTF2SVG $otf2svgParams
        }

        Write-SpectreHost "[#FFFFFF]➔ All fonts have been converted to SVG fonts.[/]"
    }
}