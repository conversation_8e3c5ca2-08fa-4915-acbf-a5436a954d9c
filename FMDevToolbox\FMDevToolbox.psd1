#
# Module manifest for module 'FMDevToolbox'
#
# Generated by: Futuremotion
#
# Generated on: 7/14/2025
#

@{

# Script module or binary module file associated with this manifest.
RootModule = 'FMDevToolbox'

# Version number of this module.
ModuleVersion = '1.0.5'

# Supported PSEditions
CompatiblePSEditions = 'Core'

# ID used to uniquely identify this module
GUID = 'ee9012b6-e539-593b-852b-1c68e2f9af70'

# Author of this module
Author = 'Futuremotion'

# Company or vendor of this module
CompanyName = 'Futuremotion'

# Copyright statement for this module
Copyright = '© Futuremotion. All rights reserved.'

# Description of the functionality provided by this module
Description = 'Provides a large amount of functions related to file processing and transformation, as well as automating development workflows.'

# Minimum version of the PowerShell engine required by this module
PowerShellVersion = '7.1'

# Name of the PowerShell host required by this module
# PowerShellHostName = ''

# Minimum version of the PowerShell host required by this module
# PowerShellHostVersion = ''

# Minimum version of Microsoft .NET Framework required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
# DotNetFrameworkVersion = ''

# Minimum version of the common language runtime (CLR) required by this module. This prerequisite is valid for the PowerShell Desktop edition only.
# ClrVersion = ''

# Processor architecture (None, X86, Amd64) required by this module
# ProcessorArchitecture = ''

# Modules that must be imported into the global environment prior to importing this module
RequiredModules = @(@{ModuleName = 'PwshSpectreConsole'; ModuleVersion = '2.3.0'; })

# Assemblies that must be loaded prior to importing this module
RequiredAssemblies = 
               'Lib\Futuremotion.FMDevToolbox\net6.0\Futuremotion.FMDevToolbox.dll', 
               'Lib\Microsoft.Toolkit.Uwp.Notifications.7.1.3\net5.0-windows10.0.17763\Microsoft.Toolkit.Uwp.Notifications.dll', 
               'Lib\Microsoft.Windows.SDK.NET.Ref.10.0.26100.34\net6.0\Microsoft.Windows.SDK.NET.dll', 
               'Lib\Ookii.Dialogs.WinForms.4.0.0\net6.0-windows7.0\Ookii.Dialogs.WinForms.dll'

# Script files (.ps1) that are run in the caller's environment prior to importing this module.
ScriptsToProcess = @()

# Type files (.ps1xml) to be loaded when importing this module
TypesToProcess = @()

# Format files (.ps1xml) to be loaded when importing this module
FormatsToProcess = @()

# Modules to import as nested modules of the module specified in RootModule/ModuleToProcess
# NestedModules = @()

# Functions to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no functions to export.
FunctionsToExport = 'Add-DetailsToObject', 'Add-NumericSuffixToFile', 
               'Add-StringSuffixToFile', 'Bootstrap-JavaScriptFramework', 
               'Clear-ConsoleWindow', 'Confirm-FolderIsGithubRepo', 
               'Confirm-ModuleIsAvailable', 'Confirm-ModuleIsLoaded', 
               'Confirm-NodeInstallationType', 'Confirm-NodeJSNormalInstall', 
               'Confirm-NPMPackageExistsInRegistry', 
               'Confirm-NVMForWindowsIsInstalled', 'Confirm-PythonFolderIsVENV', 
               'Confirm-PythonPyPiPackageExists', 'Confirm-WindowsPathIsProtected', 
               'Convert-AudioToStemsWithDEMUCS', 'Convert-ColorHexToRGB', 
               'Convert-ColorHSLToRGB', 'Convert-ColorRGBToHSV', 
               'Convert-CommaSeparatedListToPlaintextTable', 'Convert-Document', 
               'Convert-FontGlyphsToSVGs', 'Convert-FontOTFToTTF', 
               'Convert-FontToSVGFont', 'Convert-FontToTTXXML', 
               'Convert-FontTTFToOTF', 'Convert-FontWOFFCompress', 
               'Convert-FontWOFFDecompress', 'Convert-HashtableToPSObject', 
               'Convert-iTermColorsToINI', 
               'Convert-JsonKeysToCommaSeparatedString', 'Convert-JsonKeysToLines', 
               'Convert-JSXBINDecode', 'Convert-PlaintextListToPowershellArray', 
               'Convert-RegistryHexValueToString', 'Convert-SVGCropWithInkscape', 
               'Convert-SVGtoICO', 'Convert-SymbolicLinksToFiles', 
               'Convert-ToPercentage', 'Convert-VariableFontToStaticFonts', 
               'Convert-WindowsGUIDToPID', 'ConvertTo-FlatArray', 
               'ConvertTo-RegSZEscaped', 'ConvertTo-RegSZUnescaped', 
               'Copy-WindowsDirectoryStructure', 'Copy-WindowsPathsToClipboard', 
               'Expand-ArchivesInDirectory', 'Expand-EnumDefinition', 
               'Expand-ObjectPropertiesAndTypeInfo', 'Expand-ObjectTypeProperties', 
               'Find-SeparatorInList', 'Find-SymbolicLinks', 'Format-Bytes', 
               'Format-FileSize', 'Format-Hashtable', 'Format-Milliseconds', 
               'Format-NaturalSort', 'Format-ObjectSortNumerical', 'Format-String', 
               'Format-TypeConcurrentDictionary', 'Get-ANSIColorSequenceFrom1Hex', 
               'Get-ANSIColorSequenceFrom2Hex', 'Get-ANSIEscapeSequence', 
               'Get-ANSIResetSequence', 'Get-CommandFonts2SVG', 
               'Get-CommandImageMagick', 'Get-CommandInkscape', 'Get-CommandJSXER', 
               'Get-CommandNode', 'Get-CommandNPM', 'Get-CommandNVM', 
               'Get-CommandRSVGConvert', 'Get-CommandSVGO', 'Get-ConsoleWidth', 
               'Get-CPUCoresAndThreads', 'Get-EmptyDirectories', 'Get-Enum', 
               'Get-EnumsInCurrentDomain', 'Get-FirstUniqueFileByDepth', 
               'Get-ImageDimensions', 'Get-MinicondaInstallDetails', 
               'Get-ModuleConfiguration', 'Get-ModulePrivateFunctions', 
               'Get-NodeInstalledVersionDetails', 'Get-PythonInstallations', 
               'Get-PythonVENVDetails', 'Get-RandomAlphanumericString', 
               'Get-RequiredAssembliesFromModule', 'Get-Semver', 'Get-SVGDimensions', 
               'Get-UniqueNameIfDuplicate', 'Get-WindowsDefaultBrowser', 
               'Get-WindowsEnvironmentVariable', 'Get-WindowsEnvironmentVariables', 
               'Get-WindowsLicensingInformation', 'Get-WindowsOpenDirectories', 
               'Get-WindowsVersionInformation', 'Group-FontsByWidth', 
               'Initialize-FontFoundryToolsVENV', 'Initialize-NVM', 
               'Install-NodeGlobalPackagesInteractive', 
               'Install-NVMNodeGlobalPackages', 'Install-PythonGlobalPackages', 
               'Invoke-DownloadFileSpectre', 'Invoke-GUIMessageBox', 
               'Invoke-OokiiInputDialog', 'Invoke-OokiiPasswordDialog', 
               'Invoke-OokiiTaskDialog', 'Invoke-OpenFileDialog', 
               'Invoke-OpenFolderDialog', 'Invoke-SaveFileDialog', 
               'Invoke-SelfElevate', 'Invoke-VBMessageBox', 
               'Join-StringByNewlinesWithDelimiter', 
               'Move-FontFamiliesToSubfolders', 'Move-VariableFont', 
               'New-GenericObject', 'New-Log', 'New-TempDirectory', 
               'Open-WindowsExplorerTo', 'Optimize-SVGsWithSVGO', 'Out-FileHash', 
               'Publish-ImageToGithubWithUpgit', 'Read-PressAnyKeyToContinue', 
               'Register-WindowsDLLorOCX', 'Remove-AllEmptyDirectories', 
               'Remove-AllMacOSJunkFiles', 'Remove-ANSICodesFromString', 
               'Remove-DiacriticsFromString', 'Remove-EmptyDirectories', 
               'Remove-GitHubRepository', 'Remove-InvalidFilenameCharacters', 
               'Remove-NullAndEmptyValuesFromArray', 
               'Remove-UnusualSymbolsFromString', 'Rename-Fonts', 
               'Rename-ImageDensityMultiplerToActual', 'Rename-ItemUsingRegex', 
               'Rename-RandomizeFilenames', 'Rename-SanitizeFilenames', 
               'Rename-SanitizeFilenamesInFolder', 'Request-ExplorerRefreshV1', 
               'Request-ExplorerRefreshV2', 'Request-ExplorerRefreshV3', 
               'Reset-WindowsFileAssociation', 'Resolve-NPMPackageString', 
               'Resolve-RelativePath', 'Resolve-SymbolicLinks', 
               'Resolve-SymlinkTarget', 'Resolve-WindowsSIDToIdentifier', 
               'Save-Base64StringToFile', 'Save-FilesToFolderByWord', 
               'Save-FoldersInCurrentDirectory', 'Save-FolderToSubfolderByWord', 
               'Save-FontsToFolder', 'Save-RandomDataToFile', 
               'Save-RandomDataToFiles', 'Save-WindowsOpenDirectories', 
               'Search-GoogleIt', 'Set-FontEmbedLevelToUnrestricted', 
               'Set-WindowsFolderIcon', 'Show-CountdownTimer', 
               'Show-FilesBasedOnAgeInDirectory', 
               'Show-FMSpectreBorderedHeaderPanel', 'Show-HorizontalLineInConsole', 
               'Show-ImagePopup', 'Show-NVMNodeGlobalPackages', 
               'Show-NVMNodeNPMVersions', 'Show-SpectreCountdownTimer', 
               'Show-SystemOSClockResolution', 'Show-TypeHierarchy', 
               'Show-UWPToastNotification', 'Split-DirectoryContentsToSubfolders', 
               'Split-StringByDelimiter', 'Split-StringByDelimiterAndCombineLines', 
               'Stop-AdobeProcesses', 'Stop-PowershellProcesses', 'Stop-WSL', 
               'Switch-NodeVersionsWithNVM', 'Test-DirectoryContainsPwshFiles', 
               'Test-DirectoryIsEmpty', 'Test-DirectoryIsProtected', 
               'Test-FileIsLocked', 'Test-FilenameValidity', 
               'Test-FunctionAvailability', 'Test-InvalidFilenameCharacters', 
               'Test-IsElevated', 'Test-IsFontVariable', 'Test-IsValidGUID', 
               'Test-IsValidUri', 'Test-ModuleIsLoaded', 'Test-ValidLiteralPath', 
               'Test-ValidWildcardPath', 'Uninstall-NVMNodeGlobalPackages', 
               'Update-EnvironmentVariables', 
               'Update-NVMGlobalNodePackagesByVersion', 
               'Update-PythonPackagesInVENV', 'Update-PythonPIPGlobally', 
               'Update-PythonPIPInVENV', 'Update-TTFAutohintFont', 
               'Use-PythonActivateVENVInFolder', 
               'Use-PythonFreezeVENVToRequirements', 
               'Use-PythonInstallRequirementsToVENV', 
               'Add-GUIAssembliesAndEnableVisualStyles', 
               'Add-UWPNotificationsAssembly', 'Get-AvailableExceptionsList'

# Cmdlets to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no cmdlets to export.
CmdletsToExport = @()

# Variables to export from this module
VariablesToExport = '*'

# Aliases to export from this module, for best performance, do not use wildcards and do not delete the entry, use an empty array if there are no aliases to export.
AliasesToExport = 'Flatten-Array', 'Inspect-Object'

# DSC resources to export from this module
# DscResourcesToExport = @()

# List of all modules packaged with this module
# ModuleList = @()

# List of all files packaged with this module
# FileList = @()

# Private data to pass to the module specified in RootModule/ModuleToProcess. This may also contain a PSData hashtable with additional module metadata used by PowerShell.
PrivateData = @{

    PSData = @{

        # Tags applied to this module. These help with module discovery in online galleries.
        Tags = 'Files', 'Automation', 'Utility', 'Formatting', 'Registry', 'Windows', 'CLI', 
               'NodeJS', 'NVM'

        # A URL to the license for this module.
        LicenseUri = 'https://github.com/futuremotiondev/FMDevToolbox/blob/main/LICENSE'

        # A URL to the main website for this project.
        ProjectUri = 'https://github.com/futuremotiondev/FMDevToolbox'

        # A URL to an icon representing this module.
        IconUri = 'https://github.com/futuremotiondev/FMDevToolbox/blob/main/Assets/Images/ModuleIcon.png'

        # ReleaseNotes of this module
        # ReleaseNotes = ''

        # Prerelease string of this module
        # Prerelease = ''

        # Flag to indicate whether the module requires explicit user acceptance for install/update/save
        # RequireLicenseAcceptance = $false

        # External dependent modules of this module
        # ExternalModuleDependencies = @()

    } # End of PSData hashtable


} # End of PrivateData hashtable

# HelpInfo URI of this module
HelpInfoURI = 'https://github.com/futuremotiondev/FMDevToolbox/blob/main/README.md'

# Default prefix for commands exported from this module. Override the default prefix using Import-Module -Prefix.
# DefaultCommandPrefix = ''

}

