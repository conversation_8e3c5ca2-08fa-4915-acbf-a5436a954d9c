﻿using namespace System.Collections.Generic
using namespace System.IO

function Initialize-NVM {
    [CmdletBinding()]
    param ()

    $nvmCmd = Get-Command nvm.exe -CommandType Application -EA 0
    $nodeVersionDetails = Get-NodeInstalledVersionDetails -Latest
    $latestVersion = ($nodeVersionDetails.Version).ToString()
    & $nvmCmd "use" $latestVersion

    $nvmInstallDirectory      = ($nodeVersionDetails.InstallDirectory).Trim()
    $nvmInstallNodeBinaryPath = [System.IO.Path]::Combine($env:NVM_SYMLINK, 'node.exe')
    $nvmInstallNPXCmdPath     = [System.IO.Path]::Combine($env:NVM_SYMLINK, 'npx.cmd')
    $nodeCmdOutput            = Get-Command $nvmInstallNodeBinaryPath -CommandType Application
    $nvmCmdOutput             = Get-Command nvm.exe -CommandType Application
    $npxCmdOutput             = Get-Command npx.cmd -CommandType Application

    $nvmOutputObject = [PSCustomObject]@{
        ActiveNodeVersion    = $latestVersion
        ActiveNodeInstallDir = $env:NVM_SYMLINK
        ActiveNodeNVMDir     = $nvmInstallDirectory
        ActiveNodeBinaryPath = $nvmInstallNodeBinaryPath
        NodeCommand          = $nodeCmdOutput
        NodeCommandPath      = $nvmInstallNodeBinaryPath
        NVMCommand           = $nvmCmdOutput
        NVMCommandPath       = [System.IO.Path]::Combine($env:NVM_HOME, 'nvm.exe')
        NPXCommand           = $npxCmdOutput
        NPXCommandPath       = $nvmInstallNPXCmdPath
    }
    return $nvmOutputObject
}