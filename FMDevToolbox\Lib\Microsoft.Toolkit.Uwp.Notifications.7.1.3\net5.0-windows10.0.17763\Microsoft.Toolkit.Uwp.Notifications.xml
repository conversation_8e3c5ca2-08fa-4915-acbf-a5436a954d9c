<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Toolkit.Uwp.Notifications</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveGroup">
            <summary>
            Groups semantically identify that the content in the group must either be displayed as a whole, or not displayed if it cannot fit. Groups also allow creating multiple columns. Supported on Tiles since RTM. Supported on Toasts since Anniversary Update.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveGroup.Children">
            <summary>
            Gets the only valid children of groups are <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveSubgroup"/>.
            Each subgroup is displayed as a separate vertical column. Note that you must
            include at least one subgroup in your group, otherwise an <see cref="T:System.InvalidOperationException"/>
            will be thrown when you try to retrieve the XML for the notification.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Adaptive.Elements.Element_AdaptiveGroup.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlName#Name">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Adaptive.Elements.Element_AdaptiveGroup.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlChildren#Children">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Adaptive.Elements.Element_AdaptiveImage.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlName#Name">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Adaptive.Elements.Element_AdaptiveImage.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlNamedProperties#EnumerateNamedProperties">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Adaptive.Elements.Element_AdaptiveProgressBar.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlName#Name">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Adaptive.Elements.Element_AdaptiveProgressBar.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlNamedProperties#EnumerateNamedProperties">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Adaptive.Elements.Element_AdaptiveSubgroup.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlName#Name">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Adaptive.Elements.Element_AdaptiveSubgroup.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlChildren#Children">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Adaptive.Elements.Element_AdaptiveSubgroup.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlNamedProperties#EnumerateNamedProperties">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Adaptive.Elements.Element_AdaptiveText.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlName#Name">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Adaptive.Elements.Element_AdaptiveText.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlNamedProperties#EnumerateNamedProperties">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImage">
            <summary>
            An inline image.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImage.HintCrop">
            <summary>
            Gets or sets the desired cropping of the image.
            Supported on Tiles since RTM. Supported on Toast since Anniversary Update.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImage.HintRemoveMargin">
            <summary>
            Gets or sets a value whether a margin is removed. images have an 8px margin around them.
            You can remove this margin by setting this property to true.
            Supported on Tiles since RTM. Supported on Toast since Anniversary Update.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImage.HintAlign">
            <summary>
            Gets or sets the horizontal alignment of the image.
            For Toast, this is only supported when inside an <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveSubgroup"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImage.Source">
            <summary>
            Gets or sets the URI of the image (Required).
            Can be from your application package, application data, or the internet.
            Internet images must be less than 200 KB in size.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImage.AlternateText">
            <summary>
            Gets or sets a description of the image, for users of assistive technologies.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImage.AddImageQuery">
            <summary>
            Gets or sets set to true to allow Windows to append a query string to the image URI
            supplied in the Tile notification. Use this attribute if your server hosts
            images and can handle query strings, either by retrieving an image variant based
            on the query strings or by ignoring the query string and returning the image
            as specified without the query string. This query string specifies scale,
            contrast setting, and language.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImage.ToString">
            <summary>
            Returns the image's source string.
            </summary>
            <returns>The image's source string.</returns>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImageAlign">
            <summary>
            Specifies the horizontal alignment for an image.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImageAlign.Default">
            <summary>
            Default value, alignment behavior determined by renderer.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImageAlign.Stretch">
            <summary>
            Image stretches to fill available width (and potentially available height too, depending on where the image is).
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImageAlign.Left">
            <summary>
            Align the image to the left, displaying the image at its native resolution.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImageAlign.Center">
            <summary>
            Align the image in the center horizontally, displaying the image at its native resolution.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImageAlign.Right">
            <summary>
            Align the image to the right, displaying the image at its native resolution.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImageCrop">
            <summary>
            Specify the desired cropping of the image.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImageCrop.Default">
            <summary>
            Default value, cropping behavior determined by renderer.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImageCrop.None">
            <summary>
            Image is not cropped.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImageCrop.Circle">
            <summary>
            Image is cropped to a circle shape.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveProgressBar">
            <summary>
            New in Creators Update: A progress bar. Only supported on toasts on Desktop, build 15007 or newer.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveProgressBar.Title">
            <summary>
            Gets or sets an optional title string. Supports data binding.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveProgressBar.Value">
            <summary>
            Gets or sets the value of the progress bar. Supports data binding. Defaults to 0.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveProgressBar.ValueStringOverride">
            <summary>
            Gets or sets an optional string to be displayed instead of the default percentage string. If this isn't provided, something like "70%" will be displayed.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveProgressBar.Status">
            <summary>
            Gets or sets a status string (Required), which is displayed underneath the progress bar. This string should reflect the status of the operation, like "Downloading..." or "Installing..."
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveProgressBarValue">
            <summary>
            A class that represents the progress bar's value.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveProgressBarValue.BindingName">
            <summary>
            Gets or sets the property name to bind to.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveProgressBarValue.Value">
            <summary>
            Gets or sets the value (0-1) representing the percent complete.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveProgressBarValue.IsIndeterminate">
            <summary>
            Gets or sets a value indicating whether the progress bar is indeterminate.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.AdaptiveProgressBarValue.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveProgressBarValue"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveProgressBarValue.Indeterminate">
            <summary>
            Gets an indeterminate progress bar value.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.AdaptiveProgressBarValue.FromValue(System.Double)">
            <summary>
            Returns a progress bar value using the specified value (0-1) representing the percent complete.
            </summary>
            <param name="d">The value, 0-1, inclusive.</param>
            <returns>A progress bar value.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.AdaptiveProgressBarValue.FromBinding(System.String)">
            <summary>
            Returns a progress bar value using the specified binding name.
            </summary>
            <param name="bindingName">The property to bind to.</param>
            <returns>A progress bar value.</returns>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveSubgroup">
            <summary>
            Subgroups are vertical columns that can contain text and images. Supported on Tiles since RTM. Supported on Toasts since Anniversary Update.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveSubgroup.Children">
            <summary>
            Gets a list of Children. <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveText"/> and <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImage"/> are valid children of subgroups.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveSubgroup.HintWeight">
            <summary>
            Gets or sets the width of this subgroup column by specifying the weight, relative to the other subgroups.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveSubgroup.HintTextStacking">
            <summary>
            Gets or sets the vertical alignment of this subgroup's content.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveSubgroupTextStacking">
            <summary>
            TextStacking specifies the vertical alignment of content.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveSubgroupTextStacking.Default">
            <summary>
            Renderer automatically selects the default vertical alignment.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveSubgroupTextStacking.Top">
            <summary>
            Vertical align to the top.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveSubgroupTextStacking.Center">
            <summary>
            Vertical align to the center.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveSubgroupTextStacking.Bottom">
            <summary>
            Vertical align to the bottom.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveText">
            <summary>
            An adaptive text element.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveText.Text">
            <summary>
            Gets or sets the text to display. Data binding support added in Creators Update,
            only works for toast top-level text elements.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveText.Language">
            <summary>
            Gets or sets the target locale of the XML payload, specified as a BCP-47 language tags
            such as "en-US" or "fr-FR". The locale specified here overrides any other specified
            locale, such as that in binding or visual. If this value is a literal string,
            this attribute defaults to the user's UI language. If this value is a string reference,
            this attribute defaults to the locale chosen by Windows Runtime in resolving the string.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveText.HintStyle">
            <summary>
            Gets or sets the style that controls the text's font size, weight, and opacity.
            Note that for Toast, the style will only take effect if the text is inside an <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveSubgroup"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveText.HintWrap">
            <summary>
            Gets or sets a value whether text wrapping is enabled. For Tiles, this is false by default.
            For Toasts, this is true on top-level text elements, and false inside an <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveSubgroup"/>.
            Note that for Toast, setting wrap will only take effect if the text is inside an
            <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveSubgroup"/> (you can use HintMaxLines = 1 to prevent top-level text elements from wrapping).
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveText.HintMaxLines">
            <summary>
            Gets or sets the maximum number of lines the text element is allowed to display.
            For Tiles, this is infinity by default. For Toasts, top-level text elements will
            have varying max line amounts (and in the Anniversary Update you can change the max lines).
            Text on a Toast inside an <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveSubgroup"/> will behave identically to Tiles (default to infinity).
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveText.HintMinLines">
            <summary>
            Gets or sets the minimum number of lines the text element must display.
            Note that for Toast, this property will only take effect if the text is inside an <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveSubgroup"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.AdaptiveText.HintAlign">
            <summary>
            Gets or sets the horizontal alignment of the text. Note that for Toast, this property will
            only take effect if the text is inside an <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveSubgroup"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.AdaptiveText.ToString">
            <summary>
            Returns the value of the Text property.
            </summary>
            <returns>The value of the Text property.</returns>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextStyle">
            <summary>
            Text style controls font size, weight, and opacity.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextStyle.Default">
            <summary>
            Style is determined by the renderer.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextStyle.Caption">
            <summary>
            Default value. Paragraph font size, normal weight and opacity.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextStyle.CaptionSubtle">
            <summary>
            Same as Caption but with subtle opacity.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextStyle.Body">
            <summary>
            H5 font size.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextStyle.BodySubtle">
            <summary>
            Same as Body but with subtle opacity.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextStyle.Base">
            <summary>
            H5 font size, bold weight. Essentially the bold version of Body.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextStyle.BaseSubtle">
            <summary>
            Same as Base but with subtle opacity.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextStyle.Subtitle">
            <summary>
            H4 font size.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextStyle.SubtitleSubtle">
            <summary>
            Same as Subtitle but with subtle opacity.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextStyle.Title">
            <summary>
            H3 font size.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextStyle.TitleSubtle">
            <summary>
            Same as Title but with subtle opacity.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextStyle.TitleNumeral">
            <summary>
            Same as Title but with top/bottom padding removed.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextStyle.Subheader">
            <summary>
            H2 font size.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextStyle.SubheaderSubtle">
            <summary>
            Same as Subheader but with subtle opacity.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextStyle.SubheaderNumeral">
            <summary>
            Same as Subheader but with top/bottom padding removed.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextStyle.Header">
            <summary>
            H1 font size.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextStyle.HeaderSubtle">
            <summary>
            Same as Header but with subtle opacity.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextStyle.HeaderNumeral">
            <summary>
            Same as Header but with top/bottom padding removed.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextAlign">
            <summary>
            Controls the horizontal alignment of text.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextAlign.Default">
            <summary>
            Alignment is automatically determined by
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextAlign.Auto">
            <summary>
            The system automatically decides the alignment based on the language and culture.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextAlign.Left">
            <summary>
            Horizontally align the text to the left.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextAlign.Center">
            <summary>
            Horizontally align the text in the center.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextAlign.Right">
            <summary>
            Horizontally align the text to the right.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextPlacement.Inline">
            <summary>
            Default value
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.BindableProgressBarValue">
            <summary>
            A binding value for doubles.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.BindableProgressBarValue.RawValue">
            <summary>
            Gets raw value used for the implicit converter case, where dev provided a raw double. We store the raw value,
            so that later on when generating the XML, we can provide this value rather than binding syntax.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.BindableProgressBarValue.BindingName">
            <summary>
            Gets or sets the name that maps to your binding data value.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.BindableProgressBarValue.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.BindableProgressBarValue"/> class.
            A new binding for a double value, with the required binding value name. Do NOT include surrounding {} brackets.
            </summary>
            <param name="bindingName">The name that maps to your binding data value.</param>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.BindableProgressBarValue.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.BindableProgressBarValue"/> class.
            Private constructor used by the implicit converter to assign the raw value.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.BindableProgressBarValue.op_Implicit(Microsoft.Toolkit.Uwp.Notifications.AdaptiveProgressBarValue)~Microsoft.Toolkit.Uwp.Notifications.BindableProgressBarValue">
            <summary>
            Creates a <see cref="T:Microsoft.Toolkit.Uwp.Notifications.BindableProgressBarValue"/> that has a raw value assigned.
            </summary>
            <param name="v">The raw value</param>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.BindableProgressBarValue.op_Implicit(Microsoft.Toolkit.Uwp.Notifications.BindableProgressBarValue)~Microsoft.Toolkit.Uwp.Notifications.AdaptiveProgressBarValue">
            <summary>
            Returns the raw value of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.BindableProgressBarValue"/>.
            </summary>
            <param name="b">The <see cref="T:Microsoft.Toolkit.Uwp.Notifications.BindableProgressBarValue"/> to obtain the raw value from.</param>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.BindableProgressBarValue.op_Implicit(System.Double)~Microsoft.Toolkit.Uwp.Notifications.BindableProgressBarValue">
            <summary>
            Creates an <see cref="T:Microsoft.Toolkit.Uwp.Notifications.BindableProgressBarValue"/> that has the raw double value.
            </summary>
            <param name="d">The raw value</param>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.BindableString">
            <summary>
            A binding value for strings.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.BindableString.BindingName">
            <summary>
            Gets or sets the name that maps to your binding data value.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.BindableString.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.BindableString"/> class.
            A new binding for a string value, with the required binding name. Do NOT include surrounding {} brackets.
            </summary>
            <param name="bindingName">The name that maps to your data binding value.</param>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.BindableString.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.BindableString"/> class.
            Private constructor used by the implicit converter to assign the raw value.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.BindableString.op_Implicit(System.String)~Microsoft.Toolkit.Uwp.Notifications.BindableString">
            <summary>
            Creates a <see cref="T:Microsoft.Toolkit.Uwp.Notifications.BindableString"/> that has a raw value assigned.
            </summary>
            <param name="d">The raw value</param>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.BindableString.op_Implicit(Microsoft.Toolkit.Uwp.Notifications.BindableString)~System.String">
            <summary>
            Returns the raw value of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.BindableString"/>.
            </summary>
            <param name="b">The <see cref="T:Microsoft.Toolkit.Uwp.Notifications.BindableString"/> to obtain the raw value from.</param>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.IAdaptiveChild">
            <summary>
            Elements that can be direct children of adaptive content, including (<see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveText"/>, <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImage"/>, and <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveGroup"/>).
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.IAdaptiveSubgroupChild">
            <summary>
            Elements that can be direct children of an <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveSubgroup"/>, including  (<see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveText"/> and <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImage"/>).
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.IBaseImage">
            <summary>
            Contains the base properties that an image needs.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.IBaseImage.Source">
            <summary>
            Gets or sets the URI of the image. Can be from your application package, application data, or the internet. Internet images must be less than 200 KB in size.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.IBaseImage.AlternateText">
            <summary>
            Gets or sets a description of the image, for users of assistive technologies.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.IBaseImage.AddImageQuery">
            <summary>
            Gets or sets a value whether Windows should append a query string to the image URI supplied in the Tile notification. Use this attribute if your server hosts images and can handle query strings, either by retrieving an image variant based on the query strings or by ignoring the query string and returning the image as specified without the query string. This query string specifies scale, contrast setting, and language.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.IBaseText">
            <summary>
            Defines the basic properties of a text element.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.IBaseText.Text">
            <summary>
            Gets or sets the text to display.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.IBaseText.Language">
            <summary>
            Gets or sets the target locale of the XML payload, specified as a BCP-47 language tags such as "en-US" or "fr-FR". The locale specified here overrides any other specified locale, such as that in binding or visual. If this value is a literal string, this attribute defaults to the user's UI language. If this value is a string reference, this attribute defaults to the locale chosen by Windows Runtime in resolving the string.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphContent">
            <summary>
            Notification content object to display a glyph on a Tile's badge.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphContent.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphContent"/> class.
            Default constructor to create a glyph badge content object.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphContent.#ctor(Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphValue)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphContent"/> class.
            Constructor to create a glyph badge content object with a glyph.
            </summary>
            <param name="glyph">The glyph to be displayed on the badge.</param>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphContent.Glyph">
            <summary>
            Gets or sets the glyph to be displayed on the badge.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphContent.GetContent">
            <summary>
            Retrieves the notification Xml content as a string.
            </summary>
            <returns>The notification Xml content as a string.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphContent.ToString">
            <summary>
            Retrieves the notification XML content as a string.
            </summary>
            <returns>The notification XML content as a string.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphContent.GetXml">
            <summary>
            Retrieves the notification XML content as a WinRT Xml document.
            </summary>
            <returns>The notification XML content as a WinRT Xml document.</returns>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphValue">
            <summary>
            The types of glyphs that can be placed on a badge.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphValue.None">
            <summary>
            No glyph.  If there is a numeric badge, or a glyph currently on the badge,
            it will be removed.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphValue.Activity">
            <summary>
            A glyph representing application activity.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphValue.Alert">
            <summary>
            A glyph representing an alert.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphValue.Alarm">
            <summary>
            A glyph representing an alarm.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphValue.Available">
            <summary>
            A glyph representing availability status.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphValue.Away">
            <summary>
            A glyph representing away status
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphValue.Busy">
            <summary>
            A glyph representing busy status.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphValue.NewMessage">
            <summary>
            A glyph representing that a new message is available.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphValue.Paused">
            <summary>
            A glyph representing that media is paused.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphValue.Playing">
            <summary>
            A glyph representing that media is playing.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphValue.Unavailable">
            <summary>
            A glyph representing unavailable status.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphValue.Error">
            <summary>
            A glyph representing an error.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.BadgeGlyphValue.Attention">
            <summary>
            A glyph representing attention status.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.BadgeNumericContent">
            <summary>
            Notification content object to display a number on a Tile's badge.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.BadgeNumericContent.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.BadgeNumericContent"/> class.
            Default constructor to create a numeric badge content object.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.BadgeNumericContent.#ctor(System.UInt32)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.BadgeNumericContent"/> class.
            Constructor to create a numeric badge content object with a number.
            </summary>
            <param name="number">
            The number that will appear on the badge.  If the number is 0, the badge
            will be removed.
            </param>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.BadgeNumericContent.Number">
            <summary>
            Gets or sets the number that will appear on the badge. If the number is 0, the badge
            will be removed.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.BadgeNumericContent.GetContent">
            <summary>
            Retrieves the notification Xml content as a string.
            </summary>
            <returns>The notification Xml content as a string.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.BadgeNumericContent.ToString">
            <summary>
            Retrieves the notification Xml content as a string.
            </summary>
            <returns>The notification Xml content as a string.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.BadgeNumericContent.GetXml">
            <summary>
            Retrieves the notification Xml content as a WinRT Xml document.
            </summary>
            <returns>The notification Xml content as a WinRT Xml document.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.BaseElement.GetContent">
            <summary>
            Retrieves the notification XML content as a string.
            </summary>
            <returns>The notification XML content as a string.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.BaseElement.GetXml">
            <summary>
            Retrieves the notification XML content as a WinRT XML document.
            </summary>
            <returns>The notification XML content as a WinRT XML document.</returns>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.EnumFormatter">
            <summary>
            A helper class that can be used to format <see cref="T:System.Enum"/> values.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.EnumFormatter.ToPascalCaseString``1(System.Nullable{``0})">
            <summary>
            Returns a <see cref="T:System.String"/> representation of an enum value with pascal casing.
            </summary>
            <typeparam name="T">The <see cref="T:System.Enum"/> type to format.</typeparam>
            <param name="value">The <typeparamref name="T"/> value to format.</param>
            <returns>The pascal case <see cref="T:System.String"/> representation of <paramref name="value"/>.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.EnumFormatter.ToPascalCaseString``1(``0)">
            <summary>
            Returns a <see cref="T:System.String"/> representation of an enum value with pascal casing.
            </summary>
            <typeparam name="T">The <see cref="T:System.Enum"/> type to format.</typeparam>
            <param name="value">The <typeparamref name="T"/> value to format.</param>
            <returns>The pascal case <see cref="T:System.String"/> representation of <paramref name="value"/>.</returns>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.INotificationContent">
            <summary>
            Base notification content interface to retrieve notification Xml as a string.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.INotificationContent.GetContent">
            <summary>
            Retrieves the notification Xml content as a string.
            </summary>
            <returns>The notification Xml content as a string.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.INotificationContent.GetXml">
            <summary>
            Retrieves the notification Xml content as a WinRT Xml document.
            </summary>
            <returns>The notification Xml content as a WinRT Xml document.</returns>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.NotificationContentValidationException">
            <summary>
            Exception returned when invalid notification content is provided.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.IHaveXmlAdditionalProperties">
            <summary>
            An interface for a notification XML element with additional properties.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.IHaveXmlAdditionalProperties.AdditionalProperties">
            <summary>
            Gets the mapping of additional properties.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.IHaveXmlChildren">
            <summary>
            An interface for a notification XML element with additional children.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.IHaveXmlChildren.Children">
            <summary>
            Gets the children of the current element.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.IHaveXmlName">
            <summary>
            An interface for a notification XML element with a name.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.IHaveXmlName.Name">
            <summary>
            Gets the name of the current element.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.IHaveXmlNamedProperties">
            <summary>
            An interface for a notification XML element with named properties.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.IHaveXmlNamedProperties.EnumerateNamedProperties">
            <summary>
            Enumerates the available named properties for the element.
            </summary>
            <returns>A sequence of named properties for the element.</returns>
            <remarks>The returned values must be valid XML values when <see cref="M:System.Object.ToString"/> is called on them.</remarks>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.IHaveXmlText">
            <summary>
            An interface for a notification XML element with an explicit XML text content.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.IHaveXmlText.Text">
            <summary>
            Gets the text content of the current element.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.XmlWriterHelper.GetBindingOrAbsoluteXmlValue``1(System.Collections.Generic.IDictionary{``0,System.String},``0,System.String)">
            <summary>
            Gets the provided binding value, if it exists. Otherwise, falls back to the absolute value.
            </summary>
            <typeparam name="T">The type of the enum of the class properties.</typeparam>
            <param name="bindings">The collection of data-bound values.</param>
            <param name="bindableProperty">The property to obtain.</param>
            <param name="absoluteValue">The absolute value, if any.</param>
            <returns>The provided binding value, if it exists. Otherwise, falls back to the absolute value.</returns>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder">
            <summary>
            Builder class used to create <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileContent"/>
            </summary>
            <summary>
            Builder class used to create <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileContent"/>
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.AllSize">
            <summary>
            Flag used to create all tile size (Small , Medium, Large and Wide)
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.Content">
            <summary>
            Gets internal instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileContent"/>. This is equivalent to the call to <see cref="M:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.GetTileContent"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.AddTile(Microsoft.Toolkit.Uwp.Notifications.TileSize,Microsoft.Toolkit.Uwp.Notifications.ITileBindingContent)">
            <summary>
            Add a tile layout size that the notification will be displayed on.
            </summary>
            <param name="size">The size of tile that the notification will be displayed on.</param>
            <param name="tileContent">Specialized tile content. Use for special tile template. Default to NULL.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.SetBranding(Microsoft.Toolkit.Uwp.Notifications.TileBranding,Microsoft.Toolkit.Uwp.Notifications.TileSize)">
            <summary>
            Set how the tile notification should display the application branding.
            </summary>
            <param name="branding">How branding should appear on the tile</param>
            <param name="size">The tile size that the <paramref name="branding"/> parameter should be applied to. Default to all currently supported tile size.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.SetDisplayName(System.String,Microsoft.Toolkit.Uwp.Notifications.TileSize)">
            <summary>
            Set the name that will be used to override the application's name on the tile notification.
            </summary>
            <param name="displayName">Custom name to display on the tile in place of the application's name</param>
            <param name="size">The tile size that <paramref name="displayName"/> parameter should be applied to. Default to all currently supported tile size.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.SetBackgroundImage(System.Uri,Microsoft.Toolkit.Uwp.Notifications.TileSize,System.String,System.Nullable{System.Boolean},System.Nullable{System.Int32},Microsoft.Toolkit.Uwp.Notifications.TileBackgroundImageCrop)">
            <summary>
            Set the optional background image that stays behind the tile notification.
            </summary>
            <param name="imageUri">Source of the background image</param>
            <param name="size">The tile size that the background image should be applied to. Default to all currently supported tile size.</param>
            <param name="alternateText">Description of the background image, for user of assistance technology</param>
            <param name="addImageQuery">
            Indicating whether Windows should append a query string to the image URI supplied in the Tile notification.
            Use this attribute if your server hosts images and can handle query strings, either by retrieving an image variant based on the query strings or by ignoring the query string and returning the image as specified without the query string.
            This query string specifies scale, contrast setting, and language.
            </param>
            <param name="hintOverlay">The opacity of the black overlay on the background image.</param>
            <param name="hintCrop">Desired cropping of the image.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.SetBackgroundImage(Microsoft.Toolkit.Uwp.Notifications.TileBackgroundImage,Microsoft.Toolkit.Uwp.Notifications.TileSize)">
            <summary>
            Set the optional background image that stays behind the tile notification.
            </summary>
            <param name="backgroundImage">An instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileBackgroundImage"/> as the background image for the tile.</param>
            <param name="size">The tile size that the background image should be applied to. Default to all currently supported tile size.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.SetPeekImage(System.Uri,Microsoft.Toolkit.Uwp.Notifications.TileSize,System.String,System.Nullable{System.Boolean},System.Nullable{System.Int32},Microsoft.Toolkit.Uwp.Notifications.TilePeekImageCrop)">
            <summary>
            Set the Tile's Peek Image that animate from the top of the tile notification.
            </summary>
            <param name="imageUri">Source of the peek image</param>
            <param name="size">The tile size that the peek image should be applied to. Default to all currently supported tile size.</param>
            <param name="alternateText">Description of the peek image, for user of assistance technology</param>
            <param name="addImageQuery">
            Indicating whether Windows should append a query string to the image URI supplied in the Tile notification.
            Use this attribute if your server hosts images and can handle query strings, either by retrieving an image variant based on the query strings or by ignoring the query string and returning the image as specified without the query string.
            This query string specifies scale, contrast setting, and language.
            </param>
            <param name="hintOverlay">The opacity of the black overlay on the peek image.</param>
            <param name="hintCrop">Desired cropping of the image.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.SetPeekImage(Microsoft.Toolkit.Uwp.Notifications.TilePeekImage,Microsoft.Toolkit.Uwp.Notifications.TileSize)">
            <summary>
            Set the Tile's Peek Image that animate from the top of the tile notification.
            </summary>
            <param name="peekImage">An instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TilePeekImage"/> for the Tile's peek image </param>
            <param name="size">The tile size that the peek image should be applied to. Default to all currently supported tile size.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.SetTextStacking(Microsoft.Toolkit.Uwp.Notifications.TileTextStacking,Microsoft.Toolkit.Uwp.Notifications.TileSize)">
            <summary>
            Set the text stacking (vertical alignment) of the entire binding element.
            </summary>
            <param name="textStacking">Text Stacking Option</param>
            <param name="size">The tile size that the peek image should be applied to. Default to all currently supported tile size.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.SetActivationArgument(System.String,Microsoft.Toolkit.Uwp.Notifications.TileSize)">
            <summary>
            Set the tile's activation arguments for tile notification.
            </summary>
            <param name="args">App-Defined custom arguments that will be passed in when the user click on the tile when this tile notification is being displayed.</param>
            <param name="size">The tile size that the custom argument should be applied to. Default to all currently supported tile size.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.AddText(System.String,Microsoft.Toolkit.Uwp.Notifications.TileSize,System.Nullable{Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextStyle},System.Nullable{System.Boolean},System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextAlign},System.String)">
            <summary>
            Add a custom text that will appear on the tile notification.
            </summary>
            <param name="text">Custom text to display on the tile.</param>
            <param name="size">The tile size that the custom text would be added to. Default to all currently supported tile size.</param>
            <param name="hintStyle">Style that controls the text's font size, weight, and opacity.</param>
            <param name="hintWrap">Indicating whether text wrapping is enabled. For Tiles, this is false by default.</param>
            <param name="hintMaxLines">The maximum number of lines the text element is allowed to display. For Tiles, this is infinity by default</param>
            <param name="hintMinLines">The minimum number of lines the text element must display.</param>
            <param name="hintAlign">The horizontal alignment of the text</param>
            <param name="language">
            The target locale of the XML payload, specified as a BCP-47 language tags such as "en-US" or "fr-FR". The locale specified here overrides any other specified locale, such as that in binding or visual.
            </param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.AddAdaptiveTileVisualChild(Microsoft.Toolkit.Uwp.Notifications.ITileBindingContentAdaptiveChild,Microsoft.Toolkit.Uwp.Notifications.TileSize)">
            <summary>
            Add an adaptive child to the tile notification.
            </summary>
            <param name="child">An adaptive child to add</param>
            <param name="size">Tile size that the adaptive child should be added to. Default to all currently supported tile size.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder"/></returns>
            <remarks>
            This can be used to add Group and Subgroup to the tile.
            </remarks>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.GetTileContent">
            <summary>
            Get the instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileContent"/> that has been built by the builder with specified configuration so far.
            </summary>
            <returns>An instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileContent"/> that can be used to create tile notification.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.CreateContactTileContent(System.Uri,System.String,System.String,System.Nullable{System.Boolean},System.String)">
            <summary>
            Helper method for creating a tile notification content for using Contact tile template.
            </summary>
            <param name="contactImageUri">Source for the contact picture</param>
            <param name="contactName">Name of the contact</param>
            <param name="contactImageAltText">A description of the contact image, for users of assistive technologies.</param>
            <param name="contactImageAddImageQuery">Indicating whether Windows should append a query string to the image URI supplied in the Tile notification.</param>
            <param name="textLanguage">Gets or sets the target locale of the XML payload, specified as a BCP-47 language tags such as "en-US" or "fr-FR". The locale specified here overrides any other specified locale, such as that in binding or visual.</param>
            <returns>An instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentContact"/> represent a payload of a tile notification.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.CreateIconicTileContent(System.Uri,System.String,System.Nullable{System.Boolean})">
            <summary>
            Helper method for creating a tile notification content for using Iconic tile template.
            </summary>
            <param name="iconImageUri">Source of the icon image.</param>
            <param name="iconImageAltText">A description of the icon image, for users of assistive technologies.</param>
            <param name="iconImageAddImageQuery">Indicating whether Windows should append a query string to the image URI supplied in the Tile notification.</param>
            <returns>An instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentIconic"/> represent a payload of a tile notification.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.CreatePeopleTileContent(System.Uri[])">
            <summary>
            Helper method for creating a tile notification content for using People tile template.
            </summary>
            <param name="peoplePictureSources">Sources of pictures that will be used on the people tile.</param>
            <returns>An instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentPeople"/> represent a payload of a tile notification.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.CreatePeopleTileContent(System.ValueTuple{System.Uri,System.String,System.Nullable{System.Boolean}}[])">
            <summary>
            Helper method for creating a tile notification content for using People tile template.
            </summary>
            <param name="peoplePictures">Sources of pictures with description and image query indicator that will be used on the people tile.</param>
            <returns>An instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentPeople"/> represent a payload of a tile notification.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.CreatePeopleTileContent(System.Collections.Generic.IEnumerable{Microsoft.Toolkit.Uwp.Notifications.TileBasicImage})">
            <summary>
            Helper method for creating a tile notification content for using People tile template.
            </summary>
            <param name="peoplePictures">Pictures that will be used on the people tile.</param>
            <returns>An instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentPeople"/> represent a payload of a tile notification.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.CreatePhotosTileContent(System.Uri[])">
            <summary>
            Helper method for creating a tile notification content for using Photos tile template.
            </summary>
            <param name="photoSources">Sources of pictures that will be used on the photos tile.</param>
            <returns>An instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentPhotos"/> represent a payload of a tile notification.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.CreatePhotosTileContent(System.ValueTuple{System.Uri,System.String,System.Nullable{System.Boolean}}[])">
            <summary>
            Helper method for creating a tile notification content for using Photos tile template.
            </summary>
            <param name="photos">Sources of pictures with description and addImageQuery indicator that will be used for the photos tile.</param>
            <returns>An instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentPhotos"/> represent a payload of a tile notification.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContentBuilder.CreatePhotosTileContent(System.Collections.Generic.IEnumerable{Microsoft.Toolkit.Uwp.Notifications.TileBasicImage})">
            <summary>
            Helper method for creating a tile notification content for using Photos tile template.
            </summary>
            <param name="photos">Pictures that will be used for the photos tile.</param>
            <returns>An instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentPhotos"/> represent a payload of a tile notification.</returns>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_Tile.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlName#Name">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_Tile.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlChildren#Children">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_TileBinding.AddImageQuery">
             <summary>
             Gets or sets a value whether Windows should append a query string to the image URI supplied in the Tile notification. Use this attribute if your server hosts images and can handle query strings, either by retrieving an image variant based on the query strings or by ignoring the query string and returning the image as specified without the query string. This query string specifies scale, contrast setting, and language; for instance, a value of
            
             "www.website.com/images/hello.png"
            
             included in the notification becomes
            
             "www.website.com/images/hello.png?ms-scale=100&amp;ms-contrast=standard&amp;ms-lang=en-us"
             </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_TileBinding.BaseUri">
            <summary>
            Gets or sets a default base URI that is combined with relative URIs in image source attributes.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_TileBinding.Branding">
            <summary>
            Gets or sets the form that the Tile should use to display the app's brand.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_TileBinding.ContentId">
             <summary>
             Gets or sets a sender-defined string that uniquely identifies the content of the notification. This prevents duplicates in the situation where a large Tile template is displaying the last three wide Tile notifications.
            
             Required: NO
             </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_TileBinding.DisplayName">
            <summary>
            Gets or sets an optional string to override the Tile's display name while showing this notification.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_TileBinding.Language">
            <summary>
            Gets or sets the target locale of the XML payload, specified as a BCP-47 language tags such as "en-US" or "fr-FR". The locale specified here overrides that in visual, but can be overridden by that in text. If this value is a literal string, this attribute defaults to the user's UI language. If this value is a string reference, this attribute defaults to the locale chosen by Windows Runtime in resolving the string. See Remarks for when this value isn't specified.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Element_TileBinding.CheckOverlayValue(System.Int32)">
            <summary>
            Throws exception if value is invalid
            </summary>
            <param name="value">Overlay value (0-100)</param>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Element_TileBinding.Descendants">
            <summary>
            Generates an enumerable collection of children and all those children's children
            </summary>
            <returns>Enumerable collection of children and all those children's children.</returns>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_TileBinding.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlName#Name">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_TileBinding.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlChildren#Children">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Element_TileBinding.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlNamedProperties#EnumerateNamedProperties">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_TileVisual.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlName#Name">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_TileVisual.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlChildren#Children">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Element_TileVisual.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlNamedProperties#EnumerateNamedProperties">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ITileBindingContentAdaptiveChild">
            <summary>
            Elements that can be direct children of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentAdaptive"/>, including (<see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveText"/>, <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImage"/>, and <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveGroup"/>).
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentContact">
            <summary>
            Phone-only. Supported on Small, Medium, and Wide.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentContact.Image">
            <summary>
            Gets or sets the image to display.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentContact.Text">
            <summary>
            Gets or sets a line of text that is displayed. Not displayed on Small Tile.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentIconic">
            <summary>
            Supported on Small and Medium. Enables an iconic Tile template, where you can have an icon and badge display next to each other on the Tile, in true classic Windows Phone style. The number next to the icon is achieved through a separate badge notification.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentIconic.Icon">
            <summary>
            Gets or sets, at minimum, to support both Desktop and Phone, Small and Medium tiles, a square aspect ratio image with a resolution of 200x200, PNG format, with transparency and no color other than white. For more info see: http://blogs.msdn.com/b/tiles_and_toasts/archive/2015/07/31/iconic-tile-template-for-windows-10.aspx
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentPeople">
            <summary>
            New in 1511: Supported on Medium, Wide, and Large (Desktop and Mobile).
            Previously for RTM: Phone-only. Supported on Medium and Wide.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentPeople.Images">
            <summary>
            Gets images that will roll around as circles.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentPhotos">
            <summary>
            Animates through a slide show of photos. Supported on all sizes.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentPhotos.Images">
            <summary>
            Gets the collection of slide show images. Up to 12 images can be provided (Mobile will only display up to 9), which will be used for the slide show. Adding more than 12 will throw an exception.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.TileBackgroundImage">
            <summary>
            A full-bleed background image that appears beneath the Tile content.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBackgroundImage.Source">
            <summary>
            Gets or sets the URI of the image. Can be from your application package, application data, or the internet. Internet images must be less than 200 KB in size.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBackgroundImage.AlternateText">
            <summary>
            Gets or sets a description of the image, for users of assistive technologies.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBackgroundImage.AddImageQuery">
            <summary>
            Gets or sets a value whether Windows should append a query string to the image URI supplied in the Tile notification. Use this attribute if your server hosts images and can handle query strings, either by retrieving an image variant based on the query strings or by ignoring the query string and returning the image as specified without the query string. This query string specifies scale, contrast setting, and language.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBackgroundImage.HintOverlay">
            <summary>
            Gets or sets a black overlay on the background image. This value controls the opacity of the black overlay, with 0 being no overlay and 100 being completely black. Defaults to 20.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBackgroundImage.HintCrop">
            <summary>
            Gets or sets the desired cropping of the image.
            Previously for RTM: Did not exist, value will be ignored and background image will be displayed without any cropping.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.TileBasicImage">
            <summary>
            An image used on various special templates for the Tile.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBasicImage.Source">
            <summary>
            Gets or sets the URI of the image. Can be from your application package, application data, or the internet. Internet images must be less than 200 KB in size.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBasicImage.AlternateText">
            <summary>
            Gets or sets a description of the image, for users of assistive technologies.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBasicImage.AddImageQuery">
            <summary>
            Gets or sets a value whether Windows should append a query string to the image URI supplied in the Tile notification. Use this attribute if your server hosts images and can handle query strings, either by retrieving an image variant based on the query strings or by ignoring the query string and returning the image as specified without the query string. This query string specifies scale, contrast setting, and language.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.TileBasicText">
            <summary>
            A text element on the Tile.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBasicText.Text">
            <summary>
            Gets or sets the text value that will be shown in the text field.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBasicText.Lang">
            <summary>
            Gets or sets the target locale of the XML payload, specified as a BCP-47 language tags such as "en-US" or "fr-FR". The locale specified here overrides any other specified locale, such as that in binding or visual. If this value is a literal string, this attribute defaults to the user's UI language. If this value is a string reference, this attribute defaults to the locale chosen by Windows Runtime in resolving the string.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileBasicText.ToString">
            <summary>
            Returns the Text property's value.
            </summary>
            <returns>The Text property's value.</returns>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.TileBinding">
            <summary>
            The binding element contains the visual content for a specific Tile size.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBinding.Language">
            <summary>
            Gets or sets the target locale of the XML payload, specified as a BCP-47 language tags such as "en-US" or "fr-FR". The locale specified here overrides that in visual, but can be overridden by that in text. If this value is a literal string, this attribute defaults to the user's UI language. If this value is a string reference, this attribute defaults to the locale chosen by Windows Runtime in resolving the string. See Remarks for when this value isn't specified.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBinding.BaseUri">
            <summary>
            Gets or sets a default base URI that is combined with relative URIs in image source attributes. Defaults to null.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBinding.Branding">
            <summary>
            Gets or sets the form that the Tile should use to display the app's brand..
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBinding.AddImageQuery">
             <summary>
             Gets or sets a value whether Windows is allowed to append a query string to the image URI supplied in the Tile notification. Use this attribute if your server hosts images and can handle query strings, either by retrieving an image variant based on the query strings or by ignoring the query string and returning the image as specified without the query string. This query string specifies scale, contrast setting, and language; for instance, a value of
            
             "www.website.com/images/hello.png"
            
             included in the notification becomes
            
             "www.website.com/images/hello.png?ms-scale=100&amp;ms-contrast=standard&amp;ms-lang=en-us"
             </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBinding.ContentId">
            <summary>
            Gets or sets a sender-defined string that uniquely identifies the content of the notification. This prevents duplicates in the situation where a large Tile template is displaying the last three wide Tile notifications.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBinding.DisplayName">
            <summary>
            Gets or sets an optional string to override the Tile's display name while showing this notification.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBinding.Arguments">
            <summary>
            Gets or sets an app-defined data that is passed back to your app via the TileActivatedInfo property on
            LaunchActivatedEventArgs when the user launches your app from the Live Tile. This allows you to know
            which Tile notifications your user saw when they tapped your Live Tile. On devices without the Anniversary Update,
            this will simply be ignored.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBinding.Content">
            <summary>
            Gets or sets the actual content to be displayed. One of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentAdaptive"/>, <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentIconic"/>, <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentContact"/>, <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentPeople"/>, or <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentPhotos"/>
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ITileBindingContent">
            <summary>
            Visual Tile content. One of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentAdaptive"/>, <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentIconic"/>, <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentPhotos"/>, <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentPeople"/>, or <see cref="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentContact"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentAdaptive">
            <summary>
            Supported on all sizes. This is the recommended way of specifying your Tile content. Adaptive Tile templates are the de-facto choice for Windows 10, and you can create a wide variety of custom Tiles through adaptive.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentAdaptive.Children">
            <summary>
            Gets <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveText"/>, <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImage"/>, and <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveGroup"/> objects that can be added as children. The children are displayed in a vertical StackPanel fashion.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentAdaptive.BackgroundImage">
            <summary>
            Gets or sets an optional background image that gets displayed behind all the Tile content, full bleed.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentAdaptive.PeekImage">
            <summary>
            Gets or sets an optional peek image that animates in from the top of the Tile.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileBindingContentAdaptive.TextStacking">
            <summary>
            Gets or sets the text stacking (vertical alignment) of the entire binding element.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.TileBranding">
            <summary>
            The form that the Tile should use to display the app's brand.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.TileBranding.Auto">
            <summary>
            The default choice. If ShowNameOn___ is true for the Tile size being displayed, then branding will be "Name". Otherwise it will be "None".
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.TileBranding.None">
            <summary>
            No branding will be displayed.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.TileBranding.Name">
            <summary>
            The DisplayName will be shown.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.TileBranding.Logo">
            <summary>
            Desktop-only. The Square44x44Logo will be shown. On Mobile, this will fallback to Name.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.TileBranding.NameAndLogo">
            <summary>
            Desktop-only. Both the DisplayName and Square44x44Logo will be shown. On Mobile, this will fallback to Name.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.TileSize">
            <summary>
            Represent the all tile sizes that are available.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.TileSize.Small">
            <summary>
            Small Square Tile
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.TileSize.Medium">
            <summary>
            Medium Square Tile
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.TileSize.Wide">
            <summary>
            Wide Rectangle Tile
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.TileSize.Large">
            <summary>
            Large Square Tile
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.TileContent">
            <summary>
            Base Tile element, which contains a single visual element.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileContent.Visual">
            <summary>
            Gets or sets the visual element. Required.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContent.GetContent">
            <summary>
            Retrieves the notification XML content as a string, so that it can be sent with a HTTP POST in a push notification.
            </summary>
            <returns>The notification XML content as a string.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileContent.GetXml">
            <summary>
            Retrieves the notification XML content as a WinRT XmlDocument, so that it can be used with a local Tile notification's constructor on either <see cref="T:Windows.UI.Notifications.TileNotification"/> or <see cref="T:Windows.UI.Notifications.ScheduledTileNotification"/>.
            </summary>
            <returns>The notification XML content as a WinRT XmlDocument.</returns>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.TileBackgroundImageCrop">
            <summary>
            Specify the desired cropping of the image.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.TileBackgroundImageCrop.Default">
            <summary>
            Cropping style automatically determined by renderer.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.TileBackgroundImageCrop.None">
            <summary>
            Default value. Image is not cropped.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.TileBackgroundImageCrop.Circle">
            <summary>
            Image is cropped to a circle shape.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.TilePeekImageCrop">
            <summary>
            Specify the desired cropping of the image.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.TilePeekImageCrop.Default">
            <summary>
            Cropping style automatically determined by renderer.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.TilePeekImageCrop.None">
            <summary>
            Default value. Image is not cropped.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.TilePeekImageCrop.Circle">
            <summary>
            Image is cropped to a circle shape.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.TilePeekImage">
            <summary>
            A peek image that animates in from the top of the Tile.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TilePeekImage.Source">
            <summary>
            Gets or sets the URI of the image. Can be from your application package, application data, or the internet. Internet images must be less than 200 KB in size.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TilePeekImage.AlternateText">
            <summary>
            Gets or sets a description of the image, for users of assistive technologies.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TilePeekImage.AddImageQuery">
            <summary>
            Gets or sets set a value whether Windows is allowed to append a query string to the image URI supplied in the Tile notification. Use this attribute if your server hosts images and can handle query strings, either by retrieving an image variant based on the query strings or by ignoring the query string and returning the image as specified without the query string. This query string specifies scale, contrast setting, and language.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TilePeekImage.HintOverlay">
            <summary>
            Gets or sets a black overlay on the peek image. This value controls the opacity of the black overlay, with 0 being no overlay and 100 being completely black. Defaults to 0.
            Previously for RTM: Did not exist, value will be ignored and peek image will be displayed with 0 overlay.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TilePeekImage.HintCrop">
            <summary>
            Gets or sets the desired cropping of the image.
            Previously for RTM: Did not exist, value will be ignored and peek image will be displayed without any cropping.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.TileTextStacking">
            <summary>
            TextStacking specifies the vertical alignment of content.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.TileTextStacking.Top">
            <summary>
            Vertical align to the top.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.TileTextStacking.Center">
            <summary>
            Vertical align to the center.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.TileTextStacking.Bottom">
            <summary>
            Vertical align to the bottom.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.TileVisual">
            <summary>
            Contains multiple binding child elements, each of which defines a Tile.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileVisual.Language">
            <summary>
            Gets or sets the target locale of the XML payload, specified as a BCP-47 language tags such as "en-US" or "fr-FR". This locale is overridden by any locale specified in binding or text. If this value is a literal string, this attribute defaults to the user's UI language. If this value is a string reference, this attribute defaults to the locale chosen by Windows Runtime in resolving the string.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileVisual.BaseUri">
            <summary>
            Gets or sets a default base URI that is combined with relative URIs in image source attributes.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileVisual.Branding">
            <summary>
            Gets or sets the form that the Tile should use to display the app's brand.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileVisual.AddImageQuery">
             <summary>
             Gets or sets a value whether Windows is allowed to append a query string to the image URI supplied in the Tile notification. Use this attribute if your server hosts images and can handle query strings, either by retrieving an image variant based on the query strings or by ignoring the query string and returning the image as specified without the query string. This query string specifies scale, contrast setting, and language; for instance, a value of
            
             "www.website.com/images/hello.png"
            
             included in the notification becomes
            
             "www.website.com/images/hello.png?ms-scale=100&amp;ms-contrast=standard&amp;ms-lang=en-us"
             </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileVisual.ContentId">
            <summary>
            Gets or sets a sender-defined string that uniquely identifies the content of the notification. This prevents duplicates in the situation where a large Tile template is displaying the last three wide Tile notifications.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileVisual.DisplayName">
            <summary>
            Gets or sets an optional string to override the Tile's display name while showing this notification.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileVisual.LockDetailedStatus1">
            <summary>
            Gets or sets the first line of text that will be displayed on the lock screen if the user has selected
            your Tile as their detailed status app. Ff you specify this, you must also provide a Wide Tile binding.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileVisual.LockDetailedStatus2">
            <summary>
            Gets or sets the second line of text that will be displayed on the lock screen if the user has selected
            your Tile as their detailed status app. If you specify this, you must also provide a Wide Tile binding.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileVisual.LockDetailedStatus3">
            <summary>
            Gets or sets the third line of text that will be displayed on the lock screen if the user has selected your
            Tile as their detailed status app. If you specify this, you must also provide a Wide Tile binding.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileVisual.Arguments">
            <summary>
            Gets or sets app-defined data that is passed back to your app via the TileActivatedInfo property on LaunchActivatedEventArgs when the user launches your app from the Live Tile. This allows you to know which Tile notifications your user saw when they tapped your Live Tile. On devices without the Anniversary Update, this will simply be ignored.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileVisual.TileSmall">
            <summary>
            Gets or sets an optional small binding to specify content for the small Tile size.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileVisual.TileMedium">
            <summary>
            Gets or sets an optional medium binding to specify content for the medium Tile size.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileVisual.TileWide">
            <summary>
            Gets or sets an optional wide binding to specify content for the wide Tile size.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.TileVisual.TileLarge">
            <summary>
            Gets or sets an optional large binding to specify content for the large Tile size. Desktop-only
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.TileVisual.TryReuseTextElementForLockDetailedText(System.Int32,System.String,Microsoft.Toolkit.Uwp.Notifications.Element_TileBinding)">
            <summary>
            Attempts to find and re-use an existing text element inside the binding. Returns true if it could. Otherwise returns false, and the caller will have to specify the detailed status using the lock hint attribute.
            </summary>
            <param name="lineNumber">The lock screen line number.</param>
            <param name="lockText">The lock screen line text.</param>
            <param name="binding">The binding to look in for matches.</param>
            <returns>True if could re-use existing text element, otherwise false.</returns>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.CustomizeToast">
            <summary>
            Allows you to set additional properties on the <see cref="T:Windows.UI.Notifications.ToastNotification"/> object before the toast is displayed.
            </summary>
            <param name="toast">The toast to modify that will be displayed.</param>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.CustomizeToastAsync">
            <summary>
            Allows you to set additional properties on the <see cref="T:Windows.UI.Notifications.ToastNotification"/> object before the toast is displayed.
            </summary>
            <param name="toast">The toast to modify that will be displayed.</param>
            <returns>An operation.</returns>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.CustomizeScheduledToast">
            <summary>
            Allows you to set additional properties on the <see cref="T:Windows.UI.Notifications.ScheduledToastNotification"/> object before the toast is scheduled.
            </summary>
            <param name="toast">The scheduled toast to modify that will be scheduled.</param>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.CustomizeScheduledToastAsync">
            <summary>
            Allows you to set additional properties on the <see cref="T:Windows.UI.Notifications.ScheduledToastNotification"/> object before the toast is scheduled.
            </summary>
            <param name="toast">The scheduled toast to modify that will be scheduled.</param>
            <returns>An operation.</returns>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder">
            <summary>
            Builder class used to create <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContent"/>
            </summary>
            <summary>
            Builder class used to create <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContent"/>
            </summary>
            <summary>
            Builder class used to create <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContent"/>
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddButton(System.String,Microsoft.Toolkit.Uwp.Notifications.ToastActivationType,System.String)">
            <summary>
            Add a button to the current toast.
            </summary>
            <param name="content">Text to display on the button.</param>
            <param name="activationType">Type of activation this button will use when clicked. Defaults to Foreground.</param>
            <param name="arguments">App-defined string of arguments that the app can later retrieve once it is activated when the user clicks the button.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddButton(System.String,Microsoft.Toolkit.Uwp.Notifications.ToastActivationType,System.String,System.Uri)">
            <summary>
            Add a button to the current toast.
            </summary>
            <param name="content">Text to display on the button.</param>
            <param name="activationType">Type of activation this button will use when clicked. Defaults to Foreground.</param>
            <param name="arguments">App-defined string of arguments that the app can later retrieve once it is activated when the user clicks the button.</param>
            <param name="imageUri">Optional image icon for the button to display (required for buttons adjacent to inputs like quick reply).</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddButton(Microsoft.Toolkit.Uwp.Notifications.IToastButton)">
            <summary>
            Add a button to the current toast.
            </summary>
            <param name="button">An instance of class that implement <see cref="T:Microsoft.Toolkit.Uwp.Notifications.IToastButton"/> for the button that will be used on the toast.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddButton(System.String,System.String,Microsoft.Toolkit.Uwp.Notifications.ToastActivationType,System.String)">
            <summary>
            Add an button to the toast that will be display to the right of the input text box, achieving a quick reply scenario.
            </summary>
            <param name="textBoxId">ID of an existing <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastTextBox"/> in order to have this button display to the right of the input, achieving a quick reply scenario.</param>
            <param name="content">Text to display on the button.</param>
            <param name="activationType">Type of activation this button will use when clicked. Defaults to Foreground.</param>
            <param name="arguments">App-defined string of arguments that the app can later retrieve once it is activated when the user clicks the button.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddButton(System.String,System.String,Microsoft.Toolkit.Uwp.Notifications.ToastActivationType,System.String,System.Uri)">
            <summary>
            Add an button to the toast that will be display to the right of the input text box, achieving a quick reply scenario.
            </summary>
            <param name="textBoxId">ID of an existing <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastTextBox"/> in order to have this button display to the right of the input, achieving a quick reply scenario.</param>
            <param name="content">Text to display on the button.</param>
            <param name="activationType">Type of activation this button will use when clicked. Defaults to Foreground.</param>
            <param name="arguments">App-defined string of arguments that the app can later retrieve once it is activated when the user clicks the button.</param>
            <param name="imageUri">An optional image icon for the button to display (required for buttons adjacent to inputs like quick reply)</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddInputTextBox(System.String,System.String,System.String)">
            <summary>
            Add an input text box that the user can type into.
            </summary>
            <param name="id">Required ID property so that developers can retrieve user input once the app is activated.</param>
            <param name="placeHolderContent">Placeholder text to be displayed on the text box when the user hasn't typed any text yet.</param>
            <param name="title">Title text to display above the text box.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddComboBox(System.String,System.ValueTuple{System.String,System.String}[])">
            <summary>
            Add a combo box / drop-down menu that contain options for user to select.
            </summary>
            <param name="id">Required ID property used so that developers can retrieve user input once the app is activated.</param>
            <param name="choices">List of choices that will be available for user to select.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddComboBox(System.String,System.String,System.ValueTuple{System.String,System.String}[])">
            <summary>
            Add a combo box / drop-down menu that contain options for user to select.
            </summary>
            <param name="id">Required ID property used so that developers can retrieve user input once the app is activated.</param>
            <param name="defaultSelectionBoxItemId">Sets which item is selected by default, and refers to the Id property of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBoxItem"/>. If you do not provide this or null, the default selection will be empty (user sees nothing).</param>
            <param name="choices">List of choices that will be available for user to select.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddComboBox(System.String,System.String,System.String,System.ValueTuple{System.String,System.String}[])">
            <summary>
            Add a combo box / drop-down menu that contain options for user to select.
            </summary>
            <param name="id">Required ID property used so that developers can retrieve user input once the app is activated.</param>
            <param name="title">Title text to display above the Combo Box.</param>
            <param name="defaultSelectionBoxItemId">Sets which item is selected by default, and refers to the Id property of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBoxItem"/>. If you do not provide this or null, the default selection will be empty (user sees nothing).</param>
            <param name="choices">List of choices that will be available for user to select.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddComboBox(System.String,System.String,System.String,System.Collections.Generic.IEnumerable{System.ValueTuple{System.String,System.String}})">
            <summary>
            Add a combo box / drop-down menu that contain options for user to select.
            </summary>
            <param name="id">Required ID property used so that developers can retrieve user input once the app is activated.</param>
            <param name="title">Title text to display above the Combo Box.</param>
            <param name="defaultSelectionBoxItemId">Sets which item is selected by default, and refers to the Id property of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBoxItem"/>. If you do not provide this or null, the default selection will be empty (user sees nothing).</param>
            <param name="choices">List of choices that will be available for user to select.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddToastInput(Microsoft.Toolkit.Uwp.Notifications.IToastInput)">
            <summary>
            Add an input option to the Toast.
            </summary>
            <param name="input">An instance of a class that implement <see cref="T:Microsoft.Toolkit.Uwp.Notifications.IToastInput"/> that will be used on the toast.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.Content">
            <summary>
            Gets internal instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContent"/>. This is equivalent to the call to <see cref="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.GetToastContent"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddCustomTimeStamp(System.DateTime)">
            <summary>
            Add custom time stamp on the toast to override the time display on the toast.
            </summary>
            <param name="dateTime">Custom Time to be displayed on the toast</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddHeader(System.String,System.String,Microsoft.Toolkit.Uwp.Notifications.ToastArguments)">
            <summary>
            Add a header to a toast.
            </summary>
            <param name="id">A developer-created identifier that uniquely identifies this header. If two notifications have the same header id, they will be displayed underneath the same header in Action Center.</param>
            <param name="title">A title for the header.</param>
            <param name="arguments">Developer-defined arguments that are returned to the app when the user clicks this header.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
            <remarks>More info about toast header: https://docs.microsoft.com/en-us/windows/uwp/design/shell/tiles-and-notifications/toast-headers </remarks>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddHeader(System.String,System.String,System.String)">
            <summary>
            Add a header to a toast.
            </summary>
            <param name="id">A developer-created identifier that uniquely identifies this header. If two notifications have the same header id, they will be displayed underneath the same header in Action Center.</param>
            <param name="title">A title for the header.</param>
            <param name="arguments">A developer-defined string of arguments that is returned to the app when the user clicks this header.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
            <remarks>More info about toast header: https://docs.microsoft.com/en-us/windows/uwp/design/shell/tiles-and-notifications/toast-headers </remarks>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddArgument(System.String)">
            <summary>
            Adds a key (without value) to the activation arguments that will be returned when the toast notification or its buttons are clicked.
            </summary>
            <param name="key">The key.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddArgument(System.String,System.String)">
            <summary>
            Adds a key/value to the activation arguments that will be returned when the toast notification or its buttons are clicked.
            </summary>
            <param name="key">The key for this value.</param>
            <param name="value">The value itself.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddArgument(System.String,System.Int32)">
            <summary>
            Adds a key/value to the activation arguments that will be returned when the toast notification or its buttons are clicked.
            </summary>
            <param name="key">The key for this value.</param>
            <param name="value">The value itself.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddArgument(System.String,System.Double)">
            <summary>
            Adds a key/value to the activation arguments that will be returned when the toast notification or its buttons are clicked.
            </summary>
            <param name="key">The key for this value.</param>
            <param name="value">The value itself.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddArgument(System.String,System.Single)">
            <summary>
            Adds a key/value to the activation arguments that will be returned when the toast notification or its buttons are clicked.
            </summary>
            <param name="key">The key for this value.</param>
            <param name="value">The value itself.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddArgument(System.String,System.Boolean)">
            <summary>
            Adds a key/value to the activation arguments that will be returned when the toast notification or its buttons are clicked.
            </summary>
            <param name="key">The key for this value.</param>
            <param name="value">The value itself.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddArgument(System.String,System.Enum)">
            <summary>
            Adds a key/value to the activation arguments that will be returned when the toast notification or its buttons are clicked.
            </summary>
            <param name="key">The key for this value.</param>
            <param name="value">The value itself. Note that the enums are stored using their numeric value, so be aware that changing your enum number values might break existing activation of toasts currently in Action Center.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.SetProtocolActivation(System.Uri)">
            <summary>
            Configures the toast notification to launch the specified url when the toast body is clicked.
            </summary>
            <param name="protocol">The protocol to launch.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.SetProtocolActivation(System.Uri,System.String)">
            <summary>
            Configures the toast notification to launch the specified url when the toast body is clicked.
            </summary>
            <param name="protocol">The protocol to launch.</param>
            <param name="targetApplicationPfn">New in Creators Update: The target PFN, so that regardless of whether multiple apps are registered to handle the same protocol uri, your desired app will always be launched.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.SetBackgroundActivation">
            <summary>
            Configures the toast notification to use background activation when the toast body is clicked.
            </summary>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddToastActivationInfo(System.String,Microsoft.Toolkit.Uwp.Notifications.ToastActivationType)">
            <summary>
            Instead of this method, for foreground/background activation, it is suggested to use <see cref="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddArgument(System.String,System.String)"/> and optionally <see cref="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.SetBackgroundActivation"/>. For protocol activation, you should use <see cref="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.SetProtocolActivation(System.Uri)"/>. Add info that can be used by the application when the app was activated/launched by the toast.
            </summary>
            <param name="launchArgs">Custom app-defined launch arguments to be passed along on toast activation</param>
            <param name="activationType">Set the activation type that will be used when the user click on this toast</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.SetToastDuration(Microsoft.Toolkit.Uwp.Notifications.ToastDuration)">
            <summary>
            Sets the amount of time the Toast should display. You typically should use the
            Scenario attribute instead, which impacts how long a Toast stays on screen.
            </summary>
            <param name="duration">Duration of the toast</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.SetToastScenario(Microsoft.Toolkit.Uwp.Notifications.ToastScenario)">
            <summary>
             Sets the scenario, to make the Toast behave like an alarm, reminder, or more.
            </summary>
            <param name="scenario">Scenario to be used for the toast's behavior</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddAudio(System.Uri,System.Nullable{System.Boolean},System.Nullable{System.Boolean})">
            <summary>
            Set custom audio to go along with the toast.
            </summary>
            <param name="src">Source to the media that will be played when the toast is pop</param>
            <param name="loop">Indicating whether sound should repeat as long as the Toast is shown; false to play only once (default).</param>
            <param name="silent">Indicating whether sound is muted; false to allow the Toast notification sound to play (default).</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddAudio(Microsoft.Toolkit.Uwp.Notifications.ToastAudio)">
            <summary>
            Set custom audio to go along with the toast.
            </summary>
            <param name="audio">The <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastAudio"/> to set.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.GetToastContent">
            <summary>
            Get the instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContent"/> that has been built by the builder with specified configuration so far.
            </summary>
            <returns>An instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContent"/> that can be used to create tile notification.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.GetXml">
            <summary>
            Retrieves the notification XML content as a WinRT XmlDocument, so that it can be used with a local Toast notification's constructor on either <see cref="T:Windows.UI.Notifications.ToastNotification"/> or <see cref="T:Windows.UI.Notifications.ScheduledToastNotification"/>.
            </summary>
            <returns>The notification XML content as a WinRT XmlDocument.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.Show">
            <summary>
            Shows a new toast notification with the current content.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.Show(Microsoft.Toolkit.Uwp.Notifications.CustomizeToast)">
            <summary>
            Shows a new toast notification with the current content.
            </summary>
            <param name="customize">Allows you to set additional properties on the <see cref="T:Windows.UI.Notifications.ToastNotification"/> object.</param>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.Show(Microsoft.Toolkit.Uwp.Notifications.CustomizeToastAsync)">
            <summary>
            Shows a new toast notification with the current content.
            </summary>
            <param name="customize">Allows you to set additional properties on the <see cref="T:Windows.UI.Notifications.ToastNotification"/> object.</param>
            <returns>An operation that completes after your async customizations have completed.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.Schedule(System.DateTimeOffset)">
            <summary>
            Schedules the notification.
            </summary>
            <param name="deliveryTime">The date and time that Windows should display the toast notification. This time must be in the future.</param>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.Schedule(System.DateTimeOffset,Microsoft.Toolkit.Uwp.Notifications.CustomizeScheduledToast)">
            <summary>
            Schedules the notification.
            </summary>
            <param name="deliveryTime">The date and time that Windows should display the toast notification. This time must be in the future.</param>
            <param name="customize">Allows you to set additional properties on the <see cref="T:Windows.UI.Notifications.ScheduledToastNotification"/> object.</param>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.Schedule(System.DateTimeOffset,Microsoft.Toolkit.Uwp.Notifications.CustomizeScheduledToastAsync)">
            <summary>
            Schedules the notification.
            </summary>
            <param name="deliveryTime">The date and time that Windows should display the toast notification. This time must be in the future.</param>
            <param name="customize">Allows you to set additional properties on the <see cref="T:Windows.UI.Notifications.ScheduledToastNotification"/> object.</param>
            <returns>An operation that completes after your async customizations have completed.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.CreateProgressBarData(Microsoft.Toolkit.Uwp.Notifications.ToastContent,System.Int32,System.String,System.Nullable{System.Double},System.String,System.String,System.UInt32)">
            <summary>
            Create an instance of NotificationData that can be used to update toast that has a progress bar.
            </summary>
            <param name="toast">Instance of ToastContent that contain progress bars that need to be updated</param>
            <param name="index">Index of the progress bar (0-based) that this notification data is updating in the case that toast has multiple progress bars. Default to 0.</param>
            <param name="title">Title of the progress bar.</param>
            <param name="value">Value of the progress bar.</param>
            <param name="valueStringOverride">An optional string to be displayed instead of the default percentage string. If this isn't provided, something like "70%" will be displayed.</param>
            <param name="status"> A status string, which is displayed underneath the progress bar on the left. Default to empty.</param>
            <param name="sequence">A sequence number to prevent out-of-order updates, or assign 0 to indicate "always update".</param>
            <returns>An instance of NotificationData that can be used to update the toast.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddAttributionText(System.String)">
            <summary>
            Add an Attribution Text to be displayed on the toast.
            </summary>
            <param name="text">Text to be displayed as Attribution Text</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddAttributionText(System.String,System.String)">
            <summary>
            Add an Attribution Text to be displayed on the toast.
            </summary>
            <param name="text">Text to be displayed as Attribution Text</param>
            <param name="language">The target locale of the XML payload, specified as a BCP-47 language tags such as "en-US" or "fr-FR".</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddAppLogoOverride(System.Uri,System.Nullable{Microsoft.Toolkit.Uwp.Notifications.ToastGenericAppLogoCrop},System.String,System.Nullable{System.Boolean})">
            <summary>
            Override the app logo with custom image of choice that will be displayed on the toast.
            </summary>
            <param name="uri">The URI of the image. Can be from your application package, application data, or the internet. Internet images must be less than 200 KB in size.</param>
            <param name="hintCrop">Specify how the image should be cropped.</param>
            <param name="alternateText">A description of the image, for users of assistive technologies.</param>
            <param name="addImageQuery">A value whether Windows is allowed to append a query string to the image URI supplied in the Tile notification.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddHeroImage(System.Uri,System.String,System.Nullable{System.Boolean})">
            <summary>
            Add a hero image to the toast.
            </summary>
            <param name="uri">The URI of the image. Can be from your application package, application data, or the internet. Internet images must be less than 200 KB in size.</param>
            <param name="alternateText">A description of the image, for users of assistive technologies.</param>
            <param name="addImageQuery">A value whether Windows is allowed to append a query string to the image URI supplied in the Tile notification.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddInlineImage(System.Uri,System.String,System.Nullable{System.Boolean},System.Nullable{Microsoft.Toolkit.Uwp.Notifications.AdaptiveImageCrop},System.Nullable{System.Boolean})">
            <summary>
            Add an image inline with other toast content.
            </summary>
            <param name="uri">The URI of the image. Can be from your application package, application data, or the internet. Internet images must be less than 200 KB in size.</param>
            <param name="alternateText">A description of the image, for users of assistive technologies.</param>
            <param name="addImageQuery">A value whether Windows is allowed to append a query string to the image URI supplied in the Tile notification.</param>
            <param name="hintCrop">A value whether a margin is removed. images have an 8px margin around them.</param>
            <param name="hintRemoveMargin">This property is not used. Setting this has no impact.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddProgressBar(System.String,System.Nullable{System.Double},System.Boolean,System.String,System.String)">
            <summary>
            Add a progress bar to the toast.
            </summary>
            <param name="title">Title of the progress bar.</param>
            <param name="value">Value of the progress bar. Default is 0</param>
            <param name="isIndeterminate">Determine if the progress bar value should be indeterminate. Default to false.</param>
            <param name="valueStringOverride">An optional string to be displayed instead of the default percentage string. If this isn't provided, something like "70%" will be displayed.</param>
            <param name="status">A status string which is displayed underneath the progress bar. This string should reflect the status of the operation, like "Downloading..." or "Installing...". Default to empty.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
            <remarks>More info at: https://docs.microsoft.com/en-us/windows/uwp/design/shell/tiles-and-notifications/toast-progress-bar </remarks>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddText(System.String,System.Nullable{Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextStyle},System.Nullable{System.Boolean},System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{Microsoft.Toolkit.Uwp.Notifications.AdaptiveTextAlign},System.String)">
            <summary>
            Add text to the toast.
            </summary>
            <param name="text">Custom text to display on the tile.</param>
            <param name="hintStyle">This property is not used. Setting this has no effect.</param>
            <param name="hintWrap">This property is not used. Setting this has no effect. If you need to disable wrapping, set hintMaxLines to 1.</param>
            <param name="hintMaxLines">The maximum number of lines the text element is allowed to display.</param>
            <param name="hintMinLines">hintMinLines is not used. Setting this has no effect.</param>
            <param name="hintAlign">hintAlign is not used. Setting this has no effect.</param>
            <param name="language">
            The target locale of the XML payload, specified as a BCP-47 language tags such as "en-US" or "fr-FR". The locale specified here overrides any other specified locale, such as that in binding or visual.
            </param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
            <exception cref="T:System.InvalidOperationException">Throws when attempting to add/reserve more than 4 lines on a single toast. </exception>
            <exception cref="T:System.ArgumentOutOfRangeException">Throws when <paramref name="hintMaxLines"/> value is larger than 2. </exception>
            <remarks>More info at: https://docs.microsoft.com/en-us/windows/uwp/design/shell/tiles-and-notifications/adaptive-interactive-toasts#text-elements</remarks>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder.AddVisualChild(Microsoft.Toolkit.Uwp.Notifications.IToastBindingGenericChild)">
            <summary>
            Add a visual element to the toast.
            </summary>
            <param name="child">An instance of a class that implement <see cref="T:Microsoft.Toolkit.Uwp.Notifications.IToastBindingGenericChild"/>.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContentBuilder"/></returns>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.DesktopBridgeHelpers">
            <summary>
            Code from https://github.com/qmatteoq/DesktopBridgeHelpers/tree/master/DesktopBridge.Helpers/Helpers.cs
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.DesktopBridgeHelpers.IsContainerized">
            <summary>
            Returns true if the app is running in a container (MSIX) or false if not running in a container (sparse or plain Win32)
            </summary>
            <returns>Boolean</returns>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.DesktopNotificationHistoryCompat">
            <summary>
            Manages the toast notifications for an app including the ability the clear all toast history and removing individual toasts.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.DesktopNotificationHistoryCompat.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.DesktopNotificationHistoryCompat"/> class.
            Do not call this. Instead, call <see cref="P:Microsoft.Toolkit.Uwp.Notifications.DesktopNotificationManagerCompat.History"/> to obtain an instance.
            </summary>
            <param name="aumid">An AUMID that uniquely identifies your application.</param>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.DesktopNotificationHistoryCompat.Clear">
            <summary>
            Removes all notifications sent by this app from action center.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.DesktopNotificationHistoryCompat.GetHistory">
            <summary>
            Gets all notifications sent by this app that are currently still in Action Center.
            </summary>
            <returns>A collection of toasts.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.DesktopNotificationHistoryCompat.Remove(System.String)">
            <summary>
            Removes an individual toast, with the specified tag label, from action center.
            </summary>
            <param name="tag">The tag label of the toast notification to be removed.</param>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.DesktopNotificationHistoryCompat.Remove(System.String,System.String)">
            <summary>
            Removes a toast notification from the action using the notification's tag and group labels.
            </summary>
            <param name="tag">The tag label of the toast notification to be removed.</param>
            <param name="group">The group label of the toast notification to be removed.</param>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.DesktopNotificationHistoryCompat.RemoveGroup(System.String)">
            <summary>
            Removes a group of toast notifications, identified by the specified group label, from action center.
            </summary>
            <param name="group">The group label of the toast notifications to be removed.</param>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.DesktopNotificationManagerCompat">
            <summary>
            Helper for .NET Framework applications to display toast notifications and respond to toast events
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.DesktopNotificationManagerCompat.ToastActivatedLaunchArg">
            <summary>
            A constant that is used as the launch arg when your EXE is launched from a toast notification.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.DesktopNotificationManagerCompat.RegisterAumidAndComServer``1(System.String)">
            <summary>
            If you're not using MSIX or sparse packages, you must call this method to register your AUMID with the Compat library and to
            register your COM CLSID and EXE in LocalServer32 registry. Feel free to call this regardless, and we will no-op if running
            under Desktop Bridge. Call this upon application startup, before calling any other APIs.
            </summary>
            <typeparam name="T">Your implementation of NotificationActivator. Must have GUID and ComVisible attributes on class.</typeparam>
            <param name="aumid">An AUMID that uniquely identifies your application.</param>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.DesktopNotificationManagerCompat.RegisterActivator``1">
            <summary>
            Registers the activator type as a COM server client so that Windows can launch your activator.
            </summary>
            <typeparam name="T">Your implementation of NotificationActivator. Must have GUID and ComVisible attributes on class.</typeparam>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.DesktopNotificationManagerCompat.CreateToastNotifier">
            <summary>
            Creates a toast notifier. You must have called <see cref="M:Microsoft.Toolkit.Uwp.Notifications.DesktopNotificationManagerCompat.RegisterActivator``1"/> first (and also <see cref="M:Microsoft.Toolkit.Uwp.Notifications.DesktopNotificationManagerCompat.RegisterAumidAndComServer``1(System.String)"/> if you're a classic Win32 app), or this will throw an exception.
            </summary>
            <returns><see cref="T:Windows.UI.Notifications.ToastNotifier"/></returns>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.DesktopNotificationManagerCompat.History">
            <summary>
            Gets the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.DesktopNotificationHistoryCompat"/> object. You must have called <see cref="M:Microsoft.Toolkit.Uwp.Notifications.DesktopNotificationManagerCompat.RegisterActivator``1"/> first (and also <see cref="M:Microsoft.Toolkit.Uwp.Notifications.DesktopNotificationManagerCompat.RegisterAumidAndComServer``1(System.String)"/> if you're a classic Win32 app), or this will throw an exception.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.DesktopNotificationManagerCompat.CanUseHttpImages">
            <summary>
            Gets a value indicating whether http images can be used within toasts. This is true if running with package identity (MSIX or sparse package).
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.Internal.InternalNotificationActivator">
            <summary>
            Do not use this class. It must be public so that reflection can properly activate it, but consider it internal.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Internal.InternalNotificationActivator.Activate(System.String,System.String,Microsoft.Toolkit.Uwp.Notifications.Internal.InternalNotificationActivator.NOTIFICATION_USER_INPUT_DATA[],System.UInt32)">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.Internal.InternalNotificationActivator.NOTIFICATION_USER_INPUT_DATA">
            <summary>
            A single user input key/value pair.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.Internal.InternalNotificationActivator.NOTIFICATION_USER_INPUT_DATA.Key">
            <summary>
            The key of the user input.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.Internal.InternalNotificationActivator.NOTIFICATION_USER_INPUT_DATA.Value">
            <summary>
            The value of the user input.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.Internal.InternalNotificationActivator.INotificationActivationCallback">
            <summary>
            The COM callback that is triggered when your notification is clicked.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Internal.InternalNotificationActivator.INotificationActivationCallback.Activate(System.String,System.String,Microsoft.Toolkit.Uwp.Notifications.Internal.InternalNotificationActivator.NOTIFICATION_USER_INPUT_DATA[],System.UInt32)">
            <summary>
            The method called when your notification is clicked.
            </summary>
            <param name="appUserModelId">The app id of the app that sent the toast.</param>
            <param name="invokedArgs">The activation arguments from the toast.</param>
            <param name="data">The user input from the toast.</param>
            <param name="dataCount">The number of user inputs.</param>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.IApplicationResolver.GetAppIDForProcess(System.UInt32,System.String@,System.Boolean@,System.Boolean@,System.Boolean@)">
            <summary>
            Main way to obtain app ID for any process. Calls GetShortcutPathOrAppIdFromPid
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.NotificationActivator">
            <summary>
            Apps must implement this activator to handle notification activation.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.NotificationActivator.Activate(System.String,System.String,Microsoft.Toolkit.Uwp.Notifications.NotificationActivator.NOTIFICATION_USER_INPUT_DATA[],System.UInt32)">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.NotificationActivator.OnActivated(System.String,Microsoft.Toolkit.Uwp.Notifications.NotificationUserInput,System.String)">
            <summary>
            This method will be called when the user clicks on a foreground or background activation on a toast. Parent app must implement this method.
            </summary>
            <param name="arguments">The arguments from the original notification. This is either the launch argument if the user clicked the body of your toast, or the arguments from a button on your toast.</param>
            <param name="userInput">Text and selection values that the user entered in your toast.</param>
            <param name="appUserModelId">Your AUMID.</param>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.NotificationActivator.NOTIFICATION_USER_INPUT_DATA">
            <summary>
            A single user input key/value pair.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.NotificationActivator.NOTIFICATION_USER_INPUT_DATA.Key">
            <summary>
            The key of the user input.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.NotificationActivator.NOTIFICATION_USER_INPUT_DATA.Value">
            <summary>
            The value of the user input.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.NotificationActivator.INotificationActivationCallback">
            <summary>
            The COM callback that is triggered when your notification is clicked.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.NotificationActivator.INotificationActivationCallback.Activate(System.String,System.String,Microsoft.Toolkit.Uwp.Notifications.NotificationActivator.NOTIFICATION_USER_INPUT_DATA[],System.UInt32)">
            <summary>
            The method called when your notification is clicked.
            </summary>
            <param name="appUserModelId">The app id of the app that sent the toast.</param>
            <param name="invokedArgs">The activation arguments from the toast.</param>
            <param name="data">The user input from the toast.</param>
            <param name="dataCount">The number of user inputs.</param>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.NotificationUserInput">
            <summary>
            Text and selection values that the user entered on your notification. The Key is the ID of the input, and the Value is what the user entered.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.NotificationUserInput.Item(System.String)">
            <summary>
            Gets the value of an input with the given key.
            </summary>
            <param name="key">The key of the input.</param>
            <returns>The value of the input.</returns>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.NotificationUserInput.Keys">
            <summary>
            Gets all the keys of the inputs.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.NotificationUserInput.Values">
            <summary>
            Gets all the values of the inputs.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.NotificationUserInput.Count">
            <summary>
            Gets how many inputs there were.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.NotificationUserInput.ContainsKey(System.String)">
            <summary>
            Checks whether any inputs have the given key.
            </summary>
            <param name="key">The key to look for.</param>
            <returns>A boolean representing whether any inputs have the given key.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.NotificationUserInput.GetEnumerator">
            <summary>
            Gets an enumerator of the inputs.
            </summary>
            <returns>An enumerator of the inputs.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.NotificationUserInput.TryGetValue(System.String,System.String@)">
            <summary>
            Tries to get the input value for the given key.
            </summary>
            <param name="key">The key of the input to look for.</param>
            <param name="value">The value of the input.</param>
            <returns>True if found an input with the specified key, else false.</returns>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.OnActivated">
            <summary>
            Event triggered when a notification is clicked.
            </summary>
            <param name="e">Arguments that specify what action was taken and the user inputs.</param>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastNotificationActivatedEventArgsCompat">
            <summary>
            Provides information about an event that occurs when the app is activated because a user tapped on the body of a toast notification or performed an action inside a toast notification.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastNotificationActivatedEventArgsCompat.Argument">
            <summary>
            Gets the arguments from the toast XML payload related to the action that was invoked on the toast.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastNotificationActivatedEventArgsCompat.UserInput">
            <summary>
            Gets a set of values that you can use to obtain the user input from an interactive toast notification.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.Win32AppInfo.AUMID_MAX_LENGTH">
            <summary>
            If an AUMID is greater than 129 characters, scheduled toast notification APIs will throw an exception.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Win32AppInfo.Pre7_0_1Aumid">
            <summary>
            Gets the AUMID before it was fixed up with the backslash issue
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Win32AppInfo.GetAppDataFolderPath(System.String)">
            <summary>
            Gets the app data folder path within the ToastNotificationManagerCompat folder, used for storing icon assets and any additional data.
            </summary>
            <returns>Returns a string of the absolute folder path.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Win32AppInfo.GenerateGuid(System.String)">
            <summary>
            From https://stackoverflow.com/a/41622689/1454643
            Generates Guid based on String. Key assumption for this algorithm is that name is unique (across where it it's being used)
            and if name byte length is less than 16 - it will be fetched directly into guid, if over 16 bytes - then we compute sha-1
            hash from string and then pass it to guid.
            </summary>
            <param name="name">Unique name which is unique across where this guid will be used.</param>
            <returns>For example "706C7567-696E-7300-0000-000000000000" for "plugins"</returns>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastNotificationHistoryCompat">
            <summary>
            Manages the toast notifications for an app including the ability the clear all toast history and removing individual toasts.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastNotificationHistoryCompat.Clear">
            <summary>
            Removes all notifications sent by this app from action center.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastNotificationHistoryCompat.GetHistory">
            <summary>
            Gets all notifications sent by this app that are currently still in Action Center.
            </summary>
            <returns>A collection of toasts.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastNotificationHistoryCompat.Remove(System.String)">
            <summary>
            Removes an individual toast, with the specified tag label, from action center.
            </summary>
            <param name="tag">The tag label of the toast notification to be removed.</param>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastNotificationHistoryCompat.Remove(System.String,System.String)">
            <summary>
            Removes a toast notification from the action using the notification's tag and group labels.
            </summary>
            <param name="tag">The tag label of the toast notification to be removed.</param>
            <param name="group">The group label of the toast notification to be removed.</param>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastNotificationHistoryCompat.RemoveGroup(System.String)">
            <summary>
            Removes a group of toast notifications, identified by the specified group label, from action center.
            </summary>
            <param name="group">The group label of the toast notifications to be removed.</param>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastNotificationManagerCompat">
            <summary>
            Provides access to sending and managing toast notifications. Works for all types of apps, even Win32 non-MSIX/sparse apps.
            </summary>
        </member>
        <member name="E:Microsoft.Toolkit.Uwp.Notifications.ToastNotificationManagerCompat.OnActivated">
            <summary>
            Event that is triggered when a notification or notification button is clicked. Subscribe to this event in your app's initial startup code.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastNotificationManagerCompat.WasCurrentProcessToastActivated">
            <summary>
            Gets whether the current process was activated due to a toast activation. If so, the OnActivated event will be triggered soon after process launch.
            </summary>
            <returns>True if the current process was activated due to a toast activation, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastNotificationManagerCompat.CreateToastNotifier">
            <summary>
            Creates a toast notifier.
            </summary>
            <returns><see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastNotifierCompat"/>An instance of the toast notifier.</returns>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastNotificationManagerCompat.History">
            <summary>
            Gets the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastNotificationHistoryCompat"/> object.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastNotificationManagerCompat.CanUseHttpImages">
            <summary>
            Gets a value indicating whether http images can be used within toasts. This is true if running with package identity (UWP, MSIX, or sparse package).
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastNotificationManagerCompat.Uninstall">
            <summary>
            If you're not using MSIX, call this when your app is being uninstalled to properly clean up all notifications and notification-related resources. Note that this must be called from your app's main EXE (the one that you used notifications for) and not a separate uninstall EXE. If called from a MSIX app, this method no-ops.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastNotifierCompat">
            <summary>
            Allows you to show and schedule toast notifications.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastNotifierCompat.Show(Windows.UI.Notifications.ToastNotification)">
            <summary>
            Displays the specified toast notification.
            </summary>
            <param name="notification">The object that contains the content of the toast notification to display.</param>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastNotifierCompat.Hide(Windows.UI.Notifications.ToastNotification)">
            <summary>
            Hides the specified toast notification from the screen (moves it into Action Center).
            </summary>
            <param name="notification">The object that specifies the toast to hide.</param>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastNotifierCompat.AddToSchedule(Windows.UI.Notifications.ScheduledToastNotification)">
            <summary>
            Adds a ScheduledToastNotification for later display by Windows.
            </summary>
            <param name="scheduledToast">The scheduled toast notification, which includes its content and timing instructions.</param>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastNotifierCompat.RemoveFromSchedule(Windows.UI.Notifications.ScheduledToastNotification)">
            <summary>
            Cancels the scheduled display of a specified ScheduledToastNotification.
            </summary>
            <param name="scheduledToast">The notification to remove from the schedule.</param>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastNotifierCompat.GetScheduledToastNotifications">
            <summary>
            Gets the collection of ScheduledToastNotification objects that this app has scheduled for display.
            </summary>
            <returns>The collection of scheduled toast notifications that the app bound to this notifier has scheduled for timed display.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastNotifierCompat.Update(Windows.UI.Notifications.NotificationData,System.String,System.String)">
            <summary>
            Updates the existing toast notification that has the specified tag and belongs to the specified notification group.
            </summary>
            <param name="data">An object that contains the updated info.</param>
            <param name="tag">The identifier of the toast notification to update.</param>
            <param name="group">The ID of the ToastCollection that contains the notification.</param>
            <returns>A value that indicates the result of the update (failure, success, etc).</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastNotifierCompat.Update(Windows.UI.Notifications.NotificationData,System.String)">
            <summary>
            Updates the existing toast notification that has the specified tag.
            </summary>
            <param name="data">An object that contains the updated info.</param>
            <param name="tag">The identifier of the toast notification to update.</param>
            <returns>A value that indicates the result of the update (failure, success, etc).</returns>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastNotifierCompat.Setting">
            <summary>
            Gets a value that tells you whether there is an app, user, or system block that prevents the display of a toast notification.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_Toast.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlName#Name">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_Toast.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlChildren#Children">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Element_Toast.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlNamedProperties#EnumerateNamedProperties">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastDuration">
            <summary>
            The amount of time the Toast should display.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.ToastDuration.Short">
            <summary>
            Default value. Toast appears for a short while and then goes into Action Center.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.ToastDuration.Long">
            <summary>
            Toast stays on-screen for longer, and then goes into Action Center.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastScenario">
            <summary>
            Specifies the scenario, controlling behaviors about the Toast.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.ToastScenario.Default">
            <summary>
            The normal Toast behavior. The Toast appears for a short duration, and then automatically dismisses into Action Center.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.ToastScenario.Alarm">
            <summary>
            Causes the Toast to stay on-screen and expanded until the user takes action. Also causes a looping alarm sound to be selected by default.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.ToastScenario.Reminder">
            <summary>
            Causes the Toast to stay on-screen and expanded until the user takes action.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.ToastScenario.IncomingCall">
            <summary>
            Causes the Toast to stay on-screen and expanded until the user takes action (on Mobile this expands to full screen). Also causes a looping incoming call sound to be selected by default.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastAction.Content">
            <summary>
            Gets or sets the text to be displayed on the button.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastAction.Arguments">
            <summary>
            Gets or sets the arguments attribute describing the app-defined data that the app can later retrieve once it is activated from user taking this action.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastAction.ImageUri">
            <summary>
            Gets or sets optional value to provide an image icon for this action to display inside the button alone with the text content.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastAction.InputId">
            <summary>
            Gets or sets value used for the quick reply scenario.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastAction.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlName#Name">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Element_ToastAction.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlNamedProperties#EnumerateNamedProperties">
            <inheritdoc/>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.Element_ToastActivationType.Foreground">
            <summary>
            Default value. Your foreground app is launched.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.Element_ToastActivationType.Background">
            <summary>
            Your corresponding background task (assuming you set everything up) is triggered, and you can execute code in the background (like sending the user's quick reply message) without interrupting the user.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.Element_ToastActivationType.Protocol">
            <summary>
            Launch a different app using protocol activation.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.Element_ToastActivationType.System">
            <summary>
            System handles the activation.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastActions.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlName#Name">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastActions.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlChildren#Children">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Element_ToastActions.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlNamedProperties#EnumerateNamedProperties">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastAudio.Src">
            <summary>
            Gets or sets the media file to play in place of the default sound. This can either be a ms-winsoundevent value, or a custom ms-appx:/// or ms-appdata:/// file, or null for the default sound.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastAudio.Silent">
            <summary>
            Gets or sets a value indicating whether the sound is muted; false to allow the Toast notification sound to play.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastAudio.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlName#Name">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Element_ToastAudio.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlNamedProperties#EnumerateNamedProperties">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastBinding.AddImageQuery">
             <summary>
             Gets or sets a value whether Windows should append a query string to the image URI supplied in the Tile notification. Use this attribute if your server hosts images and can handle query strings, either by retrieving an image variant based on the query strings or by ignoring the query string and returning the image as specified without the query string. This query string specifies scale, contrast setting, and language; for instance, a value of
            
             "www.website.com/images/hello.png"
            
             included in the notification becomes
            
             "www.website.com/images/hello.png?ms-scale=100&amp;ms-contrast=standard&amp;ms-lang=en-us"
             </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastBinding.BaseUri">
            <summary>
            Gets or sets a default base URI that is combined with relative URIs in image source attributes.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastBinding.Language">
            <summary>
            Gets or sets the target locale of the XML payload, specified as a BCP-47 language tags such as "en-US" or "fr-FR". The locale specified here overrides that in visual, but can be overridden by that in text. If this value is a literal string, this attribute defaults to the user's UI language. If this value is a string reference, this attribute defaults to the locale chosen by Windows Runtime in resolving the string. See Remarks for when this value isn't specified.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastBinding.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlName#Name">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastBinding.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlChildren#Children">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Element_ToastBinding.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlNamedProperties#EnumerateNamedProperties">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastHeader.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlName#Name">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Element_ToastHeader.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlNamedProperties#EnumerateNamedProperties">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastImage.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlName#Name">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Element_ToastImage.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlNamedProperties#EnumerateNamedProperties">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastImageCrop">
            <summary>
            Specify the desired cropping of the image.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.ToastImageCrop.None">
            <summary>
            Default value. Image is not cropped.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.ToastImageCrop.Circle">
            <summary>
            Image is cropped to a circle shape.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastInput.Id">
            <summary>
            Gets or sets the required attributes for developers to retrieve user inputs once the app is activated (in the foreground or background).
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastInput.Title">
            <summary>
            Gets or sets the optional title attribute and is for developers to specify a title for the input for shells to render when there is affordance.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastInput.PlaceholderContent">
            <summary>
            Gets or sets the optional placeholderContent attribute and is the grey-out hint text for text input type. This attribute is ignored when the input type is not �text�.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastInput.DefaultInput">
            <summary>
            Gets or sets the optional defaultInput attribute and it allows developer to provide a default input value.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastInput.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlName#Name">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastInput.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlChildren#Children">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Element_ToastInput.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlNamedProperties#EnumerateNamedProperties">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastSelection.Id">
            <summary>
            Gets or sets the id attribute for apps to retrieve back the user selected input after the app is activated. Required
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastSelection.Content">
            <summary>
            Gets or sets the text to display for this selection element.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastSelection.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlName#Name">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Element_ToastSelection.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlNamedProperties#EnumerateNamedProperties">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastText.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlName#Name">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Element_ToastText.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlNamedProperties#EnumerateNamedProperties">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastVisual.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlName#Name">
            <inheritdoc/>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.Element_ToastVisual.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlChildren#Children">
            <inheritdoc/>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.Element_ToastVisual.Microsoft#Toolkit#Uwp#Notifications#IHaveXmlNamedProperties#EnumerateNamedProperties">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.IToastActions">
            <summary>
            Actions to display on a Toast notification. One of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastActionsCustom"/> or <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastActionsSnoozeAndDismiss"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.IToastActions.ContextMenuItems">
            <summary>
            Gets custom context menu items, providing additional actions when the user right clicks the Toast notification. New in Anniversary Update
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.IToastActivateableBuilder`1">
            <summary>
            Interfaces for classes that can have activation info added to them.
            </summary>
            <typeparam name="T">The type of the host object.</typeparam>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.IToastActivateableBuilder`1.AddArgument(System.String)">
            <summary>
            Adds a key (without value) to the activation arguments that will be returned when the content is clicked.
            </summary>
            <param name="key">The key.</param>
            <returns>The current instance of the object.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.IToastActivateableBuilder`1.AddArgument(System.String,System.String)">
            <summary>
            Adds a key/value to the activation arguments that will be returned when the content is clicked.
            </summary>
            <param name="key">The key for this value.</param>
            <param name="value">The value itself.</param>
            <returns>The current instance of the object.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.IToastActivateableBuilder`1.AddArgument(System.String,System.Int32)">
            <summary>
            Adds a key/value to the activation arguments that will be returned when the content is clicked.
            </summary>
            <param name="key">The key for this value.</param>
            <param name="value">The value itself.</param>
            <returns>The current instance of the object.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.IToastActivateableBuilder`1.AddArgument(System.String,System.Double)">
            <summary>
            Adds a key/value to the activation arguments that will be returned when the content is clicked.
            </summary>
            <param name="key">The key for this value.</param>
            <param name="value">The value itself.</param>
            <returns>The current instance of the object.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.IToastActivateableBuilder`1.AddArgument(System.String,System.Single)">
            <summary>
            Adds a key/value to the activation arguments that will be returned when the content is clicked.
            </summary>
            <param name="key">The key for this value.</param>
            <param name="value">The value itself.</param>
            <returns>The current instance of the object.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.IToastActivateableBuilder`1.AddArgument(System.String,System.Boolean)">
            <summary>
            Adds a key/value to the activation arguments that will be returned when the content is clicked.
            </summary>
            <param name="key">The key for this value.</param>
            <param name="value">The value itself.</param>
            <returns>The current instance of the object.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.IToastActivateableBuilder`1.AddArgument(System.String,System.Enum)">
            <summary>
            Adds a key/value to the activation arguments that will be returned when the content is clicked.
            </summary>
            <param name="key">The key for this value.</param>
            <param name="value">The value itself. Note that the enums are stored using their numeric value, so be aware that changing your enum number values might break existing activation of toasts currently in Action Center.</param>
            <returns>The current instance of the object.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.IToastActivateableBuilder`1.SetBackgroundActivation">
            <summary>
            Configures the content to use background activation when it is clicked.
            </summary>
            <returns>The current instance of the object.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.IToastActivateableBuilder`1.SetProtocolActivation(System.Uri)">
            <summary>
            Configures the content to use protocol activation when it is clicked.
            </summary>
            <param name="protocol">The protocol to launch.</param>
            <returns>The current instance of the object.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.IToastActivateableBuilder`1.SetProtocolActivation(System.Uri,System.String)">
            <summary>
            Configures the content to use protocol activation when it is clicked.
            </summary>
            <param name="protocol">The protocol to launch.</param>
            <param name="targetApplicationPfn">New in Creators Update: The target PFN, so that regardless of whether multiple apps are registered to handle the same protocol uri, your desired app will always be launched.</param>
            <returns>The current instance of the object.</returns>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.IToastBindingGenericChild">
            <summary>
            Elements that can be direct children of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastBindingGeneric"/>, including (<see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveText"/>, <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImage"/>, and <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveGroup"/>).
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.IToastButton">
            <summary>
            One of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/>, <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButtonSnooze"/>, or <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButtonDismiss"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.IToastButton.ImageUri">
            <summary>
            Gets or sets an optional image icon for the button to display (required for buttons adjacent to inputs like quick reply).
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.IToastButton.HintActionId">
            <summary>
            Gets or sets an identifier used in telemetry to identify your category of action. This should be something
            like "Delete", "Reply", or "Archive". In the upcoming toast telemetry dashboard in Dev Center, you will
            be able to view how frequently your actions are being clicked.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.IToastInput">
            <summary>
            An input element on a Toast notification. One of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastTextBox"/> or <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBox"/>.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastActionsCustom">
            <summary>
            Create your own custom actions, using controls like <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/>, <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastTextBox"/>, and <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBox"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastActionsCustom.Inputs">
            <summary>
            Gets inputs like <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastTextBox"/> and <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBox"/>. Only up to 5 inputs can be added; after that, an exception is thrown.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastActionsCustom.Buttons">
            <summary>
            Gets buttons displayed after all the inputs (or adjacent to inputs if used as quick reply buttons). Only up to 5 buttons can be added (or fewer if you are also including context menu items). After that, an exception is thrown. You can add <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/>, <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButtonSnooze"/>, or <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButtonDismiss"/>
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastActionsCustom.ContextMenuItems">
            <summary>
            Gets custom context menu items, providing additional actions when the user right clicks the Toast notification.
            You can only have up to 5 buttons and context menu items *combined*. Thus, if you have one context menu item,
            you can only have four buttons, etc. New in Anniversary Update:
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastActionsSnoozeAndDismiss">
            <summary>
            Automatically constructs a selection box for snooze intervals, and snooze/dismiss buttons, all automatically localized, and snoozing logic is automatically handled by the system.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastActionsSnoozeAndDismiss.ContextMenuItems">
            <summary>
            Gets custom context menu items, providing additional actions when the user right clicks the Toast notification.
            You can only have up to 5 items. New in Anniversary Update
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastActivationOptions">
            <summary>
            New in Creators Update: Additional options relating to activation.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastActivationOptions.ProtocolActivationTargetApplicationPfn">
            <summary>
            Gets or sets the target PFN if you are using <see cref="F:Microsoft.Toolkit.Uwp.Notifications.ToastActivationType.Protocol"/>. You can optionally specify, so that regardless of whether multiple apps are registered to handle the same protocol uri, your desired app will always be launched.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastActivationOptions.AfterActivationBehavior">
            <summary>
            Gets or sets the behavior that the toast should use when the user invokes this action.
            Note that this option only works on <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/> and <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContextMenuItem"/>.
            Desktop-only, supported in builds 16251 or higher. New in Fall Creators Update
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastArguments">
            <summary>
            A class that supports serializing simple key/value pairs into a format that's friendly for being used within toast notifications. The serialized format is similar to a query string, however optimized for being placed within an XML property (uses semicolons instead of ampersands since those don't need to be XML-escaped, doesn't url-encode all special characters since not being used within a URL, etc).
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.Item(System.String)">
            <summary>
            Gets the value of the specified key. Throws <see cref="T:System.Collections.Generic.KeyNotFoundException"/> if the key could not be found.
            </summary>
            <param name="key">The key to find.</param>
            <returns>The value of the specified key.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.TryGetValue(System.String,System.String@)">
            <summary>
            Attempts to get the value of the specified key. If no key exists, returns false.
            </summary>
            <param name="key">The key to find.</param>
            <param name="value">The key's value will be written here if found.</param>
            <returns>True if found the key and set the value, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.TryGetValue``1(System.String,``0@)">
            <summary>
            Attempts to get the value of the specified key. If no key exists, returns false.
            </summary>
            <typeparam name="T">The enum to parse.</typeparam>
            <param name="key">The key to find.</param>
            <param name="value">The key's value will be written here if found.</param>
            <returns>True if found the key and set the value, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.Get(System.String)">
            <summary>
            Gets the value of the specified key, or throws <see cref="T:System.Collections.Generic.KeyNotFoundException"/> if key didn't exist.
            </summary>
            <param name="key">The key to get.</param>
            <returns>The value of the key.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.GetInt(System.String)">
            <summary>
            Gets the value of the specified key, or throws <see cref="T:System.Collections.Generic.KeyNotFoundException"/> if key didn't exist.
            </summary>
            <param name="key">The key to get.</param>
            <returns>The value of the key.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.GetDouble(System.String)">
            <summary>
            Gets the value of the specified key, or throws <see cref="T:System.Collections.Generic.KeyNotFoundException"/> if key didn't exist.
            </summary>
            <param name="key">The key to get.</param>
            <returns>The value of the key.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.GetFloat(System.String)">
            <summary>
            Gets the value of the specified key, or throws <see cref="T:System.Collections.Generic.KeyNotFoundException"/> if key didn't exist.
            </summary>
            <param name="key">The key to get.</param>
            <returns>The value of the key.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.GetByte(System.String)">
            <summary>
            Gets the value of the specified key, or throws <see cref="T:System.Collections.Generic.KeyNotFoundException"/> if key didn't exist.
            </summary>
            <param name="key">The key to get.</param>
            <returns>The value of the key.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.GetBool(System.String)">
            <summary>
            Gets the value of the specified key, or throws <see cref="T:System.Collections.Generic.KeyNotFoundException"/> if key didn't exist.
            </summary>
            <param name="key">The key to get.</param>
            <returns>The value of the key.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.GetEnum``1(System.String)">
            <summary>
            Gets the value of the specified key, or throws <see cref="T:System.Collections.Generic.KeyNotFoundException"/> if key didn't exist.
            </summary>
            <typeparam name="T">The enum to parse.</typeparam>
            <param name="key">The key to get.</param>
            <returns>The value of the key.</returns>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.Count">
            <summary>
            Gets the number of key/value pairs contained in the toast arguments.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.Add(System.String)">
            <summary>
            Adds a key. If there is an existing key, it is replaced.
            </summary>
            <param name="key">The key.</param>
            <returns>The current <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastArguments"/> object.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.Add(System.String,System.String)">
            <summary>
            Adds a key and optional value. If there is an existing key, it is replaced.
            </summary>
            <param name="key">The key.</param>
            <param name="value">The optional value of the key.</param>
            <returns>The current <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastArguments"/> object.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.Add(System.String,System.Int32)">
            <summary>
            Adds a key and value. If there is an existing key, it is replaced.
            </summary>
            <param name="key">The key.</param>
            <param name="value">The value of the key.</param>
            <returns>The current <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastArguments"/> object.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.Add(System.String,System.Double)">
            <summary>
            Adds a key and value. If there is an existing key, it is replaced.
            </summary>
            <param name="key">The key.</param>
            <param name="value">The value of the key.</param>
            <returns>The current <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastArguments"/> object.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.Add(System.String,System.Single)">
            <summary>
            Adds a key and value. If there is an existing key, it is replaced.
            </summary>
            <param name="key">The key.</param>
            <param name="value">The value of the key.</param>
            <returns>The current <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastArguments"/> object.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.Add(System.String,System.Boolean)">
            <summary>
            Adds a key and value. If there is an existing key, it is replaced.
            </summary>
            <param name="key">The key.</param>
            <param name="value">The value of the key.</param>
            <returns>The current <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastArguments"/> object.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.Add(System.String,System.Enum)">
            <summary>
            Adds a key and value. If there is an existing key, it is replaced.
            </summary>
            <param name="key">The key.</param>
            <param name="value">The value of the key. Note that the enums are stored using their numeric value, so be aware that changing your enum number values might break existing activation of toasts currently in Action Center.</param>
            <returns>The current <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastArguments"/> object.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.Contains(System.String)">
            <summary>
            Determines if the specified key is present.
            </summary>
            <param name="key">The key to look for.</param>
            <returns>True if the key is present, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.Contains(System.String,System.String)">
            <summary>
            Determines if specified key and value are present.
            </summary>
            <param name="key">The key to look for.</param>
            <param name="value">The value to look for when the key has been matched.</param>
            <returns>True if the key and value were found, else false.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.Remove(System.String)">
            <summary>
            Removes the specified key and its associated value.
            </summary>
            <param name="key">The key to remove.</param>
            <returns>True if the key was removed, else false.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.Parse(System.String)">
            <summary>
            Parses a string that was generated using ToastArguments into a <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastArguments"/> object.
            </summary>
            <param name="toastArgumentsStr">The toast arguments string to deserialize.</param>
            <returns>The parsed toast arguments.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.ToString">
            <summary>
            Serializes the key-value pairs into a string that can be used within a toast notification.
            </summary>
            <returns>A string that can be used within a toast notification.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.GetEnumerator">
            <summary>
            Gets an enumerator to enumerate the arguments. Note that order of the arguments is NOT preserved.
            </summary>
            <returns>An enumeartor of the key/value pairs.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastArguments.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Gets an enumerator to enumerate the query string parameters.
            </summary>
            <returns>An enumeartor of the key/value pairs.</returns>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastAudio">
            <summary>
            Specify audio to be played when the Toast notification is received.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastAudio.Src">
            <summary>
            Gets or sets the media file to play in place of the default sound.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastAudio.Loop">
            <summary>
            Gets or sets a value indicating whether sound should repeat as long as the Toast is shown; false to play only once (default).
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastAudio.Silent">
            <summary>
            Gets or sets a value indicating whether sound is muted; false to allow the Toast notification sound to play (default).
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastBindingGeneric">
            <summary>
            Generic Toast binding, where you provide text, images, and other visual elements for your Toast notification.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastBindingGeneric.Children">
            <summary>
            Gets the contents of the body of the Toast, which can include <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveText"/>, <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveImage"/>,
            and <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveGroup"/> (added in Anniversary Update). Also, <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveText"/> elements must come
            before any other elements. If an <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveText"/> element is placed after any other element, an exception
            will be thrown when you try to retrieve the Toast XML content. And finally, certain <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveText"/> properties
            like HintStyle aren't supported on the root children text elements, and only work inside an <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveGroup"/>.
            If you use <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveGroup"/> on devices without the Anniversary Update, the group content will simply be dropped.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastBindingGeneric.AppLogoOverride">
            <summary>
            Gets or sets an optional override of the logo displayed on the Toast notification.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastBindingGeneric.HeroImage">
            <summary>
            Gets or sets an optional hero image (a visually impactful image displayed on the Toast notification).
            On devices without the Anniversary Update, the hero image will simply be ignored.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastBindingGeneric.Attribution">
            <summary>
            Gets or sets an optional text element that is displayed as attribution text. On devices without
            the Anniversary Update, this text will appear as if it's another <see cref="T:Microsoft.Toolkit.Uwp.Notifications.AdaptiveText"/>
            element at the end of your Children list.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastBindingGeneric.Language">
            <summary>
            Gets or sets the target locale of the XML payload, specified as BCP-47 language tags such as "en-US"
            or "fr-FR". This locale is overridden by any locale specified in binding or text. If this value is
            a literal string, this attribute defaults to the user's UI language. If this value is a string reference,
            this attribute defaults to the locale chosen by Windows Runtime in resolving the string.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastBindingGeneric.BaseUri">
            <summary>
            Gets or sets a default base URI that is combined with relative URIs in image source attributes.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastBindingGeneric.AddImageQuery">
            <summary>
            Gets or sets a value whether Windows is allowed to append a query string to the image URI supplied in the Toast notification. Use this attribute if your server hosts images and can handle query strings, either by retrieving an image variant based on the query strings or by ignoring the query string and returning the image as specified without the query string. This query string specifies scale, contrast setting, and language.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastBindingShoulderTap">
            <summary>
            Specifies content you want to appear in a My People shoulder tap notification. For more info, see the My People notifications documentation. New in Fall Creators Update.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastBindingShoulderTap.Image">
            <summary>
            Gets or sets the image to be displayed in the shoulder tap notification. Required.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastBindingShoulderTap.Language">
            <summary>
            Gets or sets the target locale of the XML payload, specified as BCP-47 language tags such as "en-US"
            or "fr-FR". This locale is overridden by any locale specified in binding or text. If this value is
            a literal string, this attribute defaults to the user's UI language. If this value is a string reference,
            this attribute defaults to the locale chosen by Windows Runtime in resolving the string.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastBindingShoulderTap.BaseUri">
            <summary>
            Gets or sets a default base URI that is combined with relative URIs in image source attributes.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastBindingShoulderTap.AddImageQuery">
            <summary>
            Gets or sets a value whether Windows is allowed to append a query string to the image URI supplied in the Toast notification. Use this attribute if your server hosts images and can handle query strings, either by retrieving an image variant based on the query strings or by ignoring the query string and returning the image as specified without the query string. This query string specifies scale, contrast setting, and language.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton">
            <summary>
            A button that the user can click on a Toast notification.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButton.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/> class.
            </summary>
            <param name="content">The text to display on the button.</param>
            <param name="arguments">App-defined string of arguments that the app can later retrieve once it is activated when the user clicks the button.</param>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButton.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastButton.Content">
            <summary>
            Gets the text to display on the button. Required
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastButton.Arguments">
            <summary>
            Gets app-defined string of arguments that the app can later retrieve once it is
            activated when the user clicks the button. Required
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastButton.ActivationType">
            <summary>
            Gets or sets what type of activation this button will use when clicked. Defaults to Foreground.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastButton.ActivationOptions">
            <summary>
            Gets or sets additional options relating to activation of the toast button. New in Creators Update
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastButton.ImageUri">
            <summary>
            Gets or sets an optional image icon for the button to display (required for buttons adjacent to inputs like quick reply).
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastButton.TextBoxId">
            <summary>
            Gets or sets the ID of an existing <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastTextBox"/> in order to have this button display
            to the right of the input, achieving a quick reply scenario.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastButton.HintActionId">
            <summary>
            Gets or sets an identifier used in telemetry to identify your category of action. This should be something
            like "Delete", "Reply", or "Archive". In the upcoming toast telemetry dashboard in Dev Center, you will
            be able to view how frequently your actions are being clicked.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButton.SetContent(System.String)">
            <summary>
            Sets the text to display on the button.
            </summary>
            <param name="content">The text to display on the button.</param>
            <returns>The current instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/>.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButton.AddArgument(System.String)">
            <summary>
            Adds a key (without value) to the activation arguments that will be returned when the toast notification or its buttons are clicked.
            </summary>
            <param name="key">The key.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButton.AddArgument(System.String,System.String)">
            <summary>
            Adds a key/value to the activation arguments that will be returned when the toast notification or its buttons are clicked.
            </summary>
            <param name="key">The key for this value.</param>
            <param name="value">The value itself.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButton.AddArgument(System.String,System.Int32)">
            <summary>
            Adds a key/value to the activation arguments that will be returned when the toast notification or its buttons are clicked.
            </summary>
            <param name="key">The key for this value.</param>
            <param name="value">The value itself.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButton.AddArgument(System.String,System.Double)">
            <summary>
            Adds a key/value to the activation arguments that will be returned when the toast notification or its buttons are clicked.
            </summary>
            <param name="key">The key for this value.</param>
            <param name="value">The value itself.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButton.AddArgument(System.String,System.Single)">
            <summary>
            Adds a key/value to the activation arguments that will be returned when the toast notification or its buttons are clicked.
            </summary>
            <param name="key">The key for this value.</param>
            <param name="value">The value itself.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButton.AddArgument(System.String,System.Boolean)">
            <summary>
            Adds a key/value to the activation arguments that will be returned when the toast notification or its buttons are clicked.
            </summary>
            <param name="key">The key for this value.</param>
            <param name="value">The value itself.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButton.AddArgument(System.String,System.Enum)">
            <summary>
            Adds a key/value to the activation arguments that will be returned when the toast notification or its buttons are clicked.
            </summary>
            <param name="key">The key for this value.</param>
            <param name="value">The value itself. Note that the enums are stored using their numeric value, so be aware that changing your enum number values might break existing activation of toasts currently in Action Center.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButton.SetProtocolActivation(System.Uri)">
            <summary>
            Configures the button to launch the specified url when the button is clicked.
            </summary>
            <param name="protocol">The protocol to launch.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButton.SetProtocolActivation(System.Uri,System.String)">
            <summary>
            Configures the button to launch the specified url when the button is clicked.
            </summary>
            <param name="protocol">The protocol to launch.</param>
            <param name="targetApplicationPfn">New in Creators Update: The target PFN, so that regardless of whether multiple apps are registered to handle the same protocol uri, your desired app will always be launched.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButton.SetBackgroundActivation">
            <summary>
            Configures the button to use background activation when the button is clicked.
            </summary>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButton.SetAfterActivationBehavior(Microsoft.Toolkit.Uwp.Notifications.ToastAfterActivationBehavior)">
            <summary>
            Sets the behavior that the toast should use when the user invokes this button. Desktop-only, supported in builds 16251 or higher. New in Fall Creators Update.
            </summary>
            <param name="afterActivationBehavior">The behavior that the toast should use when the user invokes this button.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButton.SetSnoozeActivation">
            <summary>
            Configures the button to use system snooze activation when the button is clicked, using the default system snooze time.
            </summary>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButton.SetSnoozeActivation(System.String)">
            <summary>
            Configures the button to use system snooze activation when the button is clicked, with a snooze time defined by the specified selection box.
            </summary>
            <param name="selectionBoxId">The ID of an existing <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBox"/> which allows the user to pick a custom snooze time. The ID's of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBoxItem"/>s inside the selection box must represent the snooze interval in minutes. For example, if the user selects an item that has an ID of "120", then the notification will be snoozed for 2 hours. When the user clicks this button, if you specified a SelectionBoxId, the system will parse the ID of the selected item and snooze by that amount of minutes.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButton.SetDismissActivation">
            <summary>
            Configures the button to use system dismiss activation when the button is clicked (the toast will simply dismiss rather than activating).
            </summary>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButton.SetHintActionId(System.String)">
            <summary>
            Sets an identifier used in telemetry to identify your category of action. This should be something like "Delete", "Reply", or "Archive". In the upcoming toast telemetry dashboard in Dev Center, you will be able to view how frequently your actions are being clicked.
            </summary>
            <param name="actionId">An identifier used in telemetry to identify your category of action.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButton.SetImageUri(System.Uri)">
            <summary>
            Sets an optional image icon for the button to display (required for buttons adjacent to inputs like quick reply).
            </summary>
            <param name="imageUri">An optional image icon for the button to display.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/></returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButton.SetTextBoxId(System.String)">
            <summary>
            Sets the ID of an existing <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastTextBox"/> in order to have this button display to the right of the input, achieving a quick reply scenario.
            </summary>
            <param name="textBoxId">The ID of an existing <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastTextBox"/>.</param>
            <returns>The current instance of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButton"/></returns>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastButtonDismiss">
            <summary>
            A button that, when clicked, is interpreted as a "dismiss" by the system, and the Toast is dismissed just like if the user swiped the Toast away.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastButtonDismiss.CustomContent">
            <summary>
            Gets custom text displayed on the button that overrides the default localized "Dismiss" text.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastButtonDismiss.ImageUri">
            <summary>
            Gets or sets an optional image icon for the button to display.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastButtonDismiss.HintActionId">
            <summary>
            Gets or sets an identifier used in telemetry to identify your category of action. This should be something
            like "Delete", "Reply", or "Archive". In the upcoming toast telemetry dashboard in Dev Center, you will
            be able to view how frequently your actions are being clicked.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButtonDismiss.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButtonDismiss"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButtonDismiss.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButtonDismiss"/> class.
            Constructs a system-handled dismiss button that displays your text on the button.
            </summary>
            <param name="customContent">The text you want displayed on the button.</param>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastButtonSnooze">
            <summary>
            A system-handled snooze button that automatically handles snoozing of a Toast notification.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastButtonSnooze.CustomContent">
            <summary>
            Gets custom text displayed on the button that overrides the default localized "Snooze" text.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastButtonSnooze.ImageUri">
            <summary>
            Gets or sets an optional image icon for the button to display.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastButtonSnooze.SelectionBoxId">
            <summary>
            Gets or sets the ID of an existing <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBox"/> in order to allow the
            user to pick a custom snooze time. Optional. The ID's of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBoxItem"/>s
            inside the selection box must represent the snooze interval in minutes. For example,
            if the user selects an item that has an ID of "120", then the notification will be snoozed
            for 2 hours. When the user clicks this button, if you specified a SelectionBoxId, the system
            will parse the ID of the selected item and snooze by that amount of minutes. If you didn't specify
            a SelectionBoxId, the system will snooze by the default system snooze time.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastButtonSnooze.HintActionId">
            <summary>
            Gets or sets an identifier used in telemetry to identify your category of action. This should be something
            like "Delete", "Reply", or "Archive". In the upcoming toast telemetry dashboard in Dev Center, you will
            be able to view how frequently your actions are being clicked.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButtonSnooze.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButtonSnooze"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastButtonSnooze.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastButtonSnooze"/> class.
            Initializes a system-handled snooze button that displays your text on the button and automatically handles snoozing.
            </summary>
            <param name="customContent">The text you want displayed on the button.</param>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastActivationType">
            <summary>
            Decides the type of activation that will be used when the user interacts with the Toast notification.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.ToastActivationType.Foreground">
            <summary>
            Default value. Your foreground app is launched.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.ToastActivationType.Background">
            <summary>
            Your corresponding background task (assuming you set everything up) is triggered, and you can execute code in the background (like sending the user's quick reply message) without interrupting the user.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.ToastActivationType.Protocol">
            <summary>
            Launch a different app using protocol activation.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastAfterActivationBehavior">
            <summary>
            Specifies the behavior that the toast should use when the user takes action on the toast.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.ToastAfterActivationBehavior.Default">
            <summary>
            Default behavior. The toast will be dismissed when the user takes action on the toast.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.ToastAfterActivationBehavior.PendingUpdate">
            <summary>
            After the user clicks a button on your toast, the notification will remain present, in a "pending update" visual state. You should immediately update your toast from a background task so that the user does not see this "pending update" visual state for too long.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastContent">
            <summary>
            Base Toast element, which contains at least a visual element.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastContent.Visual">
            <summary>
            Gets or sets the visual element (Required).
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastContent.Audio">
            <summary>
            Gets or sets custom audio options.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastContent.Actions">
            <summary>
            Gets or sets optional custom actions with buttons and inputs (using <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastActionsCustom"/>)
            or optionally use the system-default snooze/dismiss controls (with <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastActionsSnoozeAndDismiss"/>).
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastContent.Header">
            <summary>
            Gets or sets an optional header for the toast notification. Requires Creators Update
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastContent.Scenario">
            <summary>
            Gets or sets the scenario, to make the Toast behave like an alarm, reminder, or more.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastContent.Duration">
            <summary>
            Gets or sets the amount of time the Toast should display. You typically should use the
            Scenario attribute instead, which impacts how long a Toast stays on screen.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastContent.Launch">
            <summary>
            Gets or sets a string that is passed to the application when it is activated by the Toast.
            The format and contents of this string are defined by the app for its own use. When the user
            taps or clicks the Toast to launch its associated app, the launch string provides the context
            to the app that allows it to show the user a view relevant to the Toast content, rather than
            launching in its default way.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastContent.ActivationType">
            <summary>
            Gets or sets what activation type will be used when the user clicks the body of this Toast.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastContent.ActivationOptions">
            <summary>
            Gets or sets additional options relating to activation of the toast notification. Requires Creators Updated
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastContent.DisplayTimestamp">
            <summary>
            Gets or sets an optional custom time to use for the notification's timestamp, visible within Action Center.
            If provided, this date/time will be used on the notification instead of the date/time that the notification was received.
            Requires Creators Update
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastContent.HintToastId">
            <summary>
            Gets or sets an identifier used in telemetry to identify your category of toast notification. This should be something
            like "NewMessage", "AppointmentReminder", "Promo30Off", or "PleaseRate". In the upcoming toast telemetry dashboard
            in Dev Center, you will be able to view activation info filtered by toast identifier.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastContent.HintPeople">
            <summary>
            Gets or sets the person that this toast is related to. For more info, see the My People documentation. New in Fall Creators Update.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastContent.AdditionalProperties">
            <summary>
            Gets a dictionary where you can assign additional properties.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContent.GetContent">
            <summary>
            Retrieves the notification XML content as a string, so that it can be sent with a HTTP POST in a push notification.
            </summary>
            <returns>The notification XML content as a string.</returns>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContent.GetXml">
            <summary>
            Retrieves the notification XML content as a WinRT XmlDocument, so that it can be used with a local Toast notification's constructor on either <see cref="T:Windows.UI.Notifications.ToastNotification"/> or <see cref="T:Windows.UI.Notifications.ScheduledToastNotification"/>.
            </summary>
            <returns>The notification XML content as a WinRT XmlDocument.</returns>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastContextMenuItem">
            <summary>
            A Toast context menu item.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastContextMenuItem.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastContextMenuItem"/> class.
            A Toast context menu item with the required properties.
            </summary>
            <param name="content">The text to display on the menu item.</param>
            <param name="arguments">App-defined string of arguments that the app can later retrieve once it is activated when the user clicks the menu item.</param>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastContextMenuItem.Content">
            <summary>
            Gets the text to display on the menu item. Required
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastContextMenuItem.Arguments">
            <summary>
            Gets app-defined string of arguments that the app can later retrieve once it is activated when the user clicks the menu item. Required
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastContextMenuItem.ActivationType">
            <summary>
            Gets or sets what type of activation this menu item will use when clicked. Defaults to Foreground.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastContextMenuItem.ActivationOptions">
            <summary>
            Gets or sets additional options relating to activation of the toast context menu item. New in Creators Update
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastContextMenuItem.HintActionId">
            <summary>
            Gets or sets an identifier used in telemetry to identify your category of action. This should be something
            like "TurnOff" or "ManageSettings". In the upcoming toast telemetry dashboard in Dev Center, you will
            be able to view how frequently your actions are being clicked.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastGenericAppLogo">
            <summary>
            The logo that is displayed on your Toast notification.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastGenericAppLogo.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastGenericAppLogo"/> class,
            a logo that is displayed on your Toast notification.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastGenericAppLogo.Source">
            <summary>
            Gets or sets the URI of the image. Can be from your application package, application data, or the internet. Internet images must be less than 200 KB in size.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastGenericAppLogo.AlternateText">
            <summary>
            Gets or sets a description of the image, for users of assistive technologies.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastGenericAppLogo.AddImageQuery">
            <summary>
            Gets or sets set a value whether Windows is allowed to append a query string to the image URI supplied in the Tile notification. Use this attribute if your server hosts images and can handle query strings, either by retrieving an image variant based on the query strings or by ignoring the query string and returning the image as specified without the query string. This query string specifies scale, contrast setting, and language.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastGenericAppLogo.HintCrop">
            <summary>
            Gets or sets specify how the image should be cropped.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastGenericAppLogoCrop">
            <summary>
            Specify the desired cropping of the image.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.ToastGenericAppLogoCrop.Default">
            <summary>
            Cropping uses the default behavior of the renderer.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.ToastGenericAppLogoCrop.None">
            <summary>
            Image is not cropped.
            </summary>
        </member>
        <member name="F:Microsoft.Toolkit.Uwp.Notifications.ToastGenericAppLogoCrop.Circle">
            <summary>
            Image is cropped to a circle shape.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastGenericAttributionText">
            <summary>
            Defines an attribution text element to be displayed on the Toast notification.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastGenericAttributionText.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastGenericAttributionText"/> class.
            An attribution text element to be displayed on the Toast notification.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastGenericAttributionText.Text">
            <summary>
            Gets or sets the text to display.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastGenericAttributionText.Language">
            <summary>
            Gets or sets the target locale of the XML payload, specified as a BCP-47 language tags such as "en-US" or "fr-FR". The locale specified here overrides any other specified locale, such as that in binding or visual. If this value is a literal string, this attribute defaults to the user's UI language. If this value is a string reference, this attribute defaults to the locale chosen by Windows Runtime in resolving the string.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastGenericHeroImage">
            <summary>
            A hero image for the Toast notification.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastGenericHeroImage.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastGenericHeroImage"/> class.
            A hero image for the Toast notification.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastGenericHeroImage.Source">
            <summary>
            Gets or sets the URI of the image. Can be from your application package, application data, or the internet. Internet images must be less than 200 KB in size.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastGenericHeroImage.AlternateText">
            <summary>
            Gets or sets a description of the image, for users of assistive technologies.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastGenericHeroImage.AddImageQuery">
            <summary>
            Gets or sets a value whether Windows is allowed to append a query string to the image URI supplied in the Tile notification. Use this attribute if your server hosts images and can handle query strings, either by retrieving an image variant based on the query strings or by ignoring the query string and returning the image as specified without the query string. This query string specifies scale, contrast setting, and language.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastHeader">
            <summary>
            Defines a visual header for the toast notification.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastHeader.#ctor(System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastHeader"/> class.
            Constructs a toast header with all the required properties.
            </summary>
            <param name="id">A developer-created identifier that uniquely identifies this header. If two notifications have the same header id, they will be displayed underneath the same header in Action Center.</param>
            <param name="title">A title for the header.</param>
            <param name="arguments">A developer-defined string of arguments that is returned to the app when the user clicks this header.</param>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastHeader.Id">
            <summary>
            Gets or sets a developer-created identifier that uniquely identifies this header. If two notifications have the same header id, they will be displayed underneath the same header in Action Center. Cannot be null.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastHeader.Title">
            <summary>
            Gets or sets a title for the header. Cannot be null.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastHeader.Arguments">
            <summary>
            Gets or sets a developer-defined string of arguments that is returned to the app when the user clicks this header. Cannot be null.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastHeader.ActivationType">
            <summary>
            Gets or sets the type of activation this header will use when clicked. Defaults to Foreground. Note that only Foreground and Protocol are supported.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastHeader.ActivationOptions">
            <summary>
            Gets or sets additional options relating to activation of the toast header. New in Creators Update
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastPeople">
            <summary>
            Specify what person this toast is related to. For more info, see the My People documentation. New in Fall Creators Update.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastPeople.RemoteId">
            <summary>
            Gets or sets a remote identifier that corresponds with the RemoteId you set on a Contact you created
            with the ContactStore APIs. For more info, see the My People documentation.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastPeople.EmailAddress">
            <summary>
            Gets or sets an email address that corresponds with a contact in the local Windows ContactStore. Note
            that if <see cref="P:Microsoft.Toolkit.Uwp.Notifications.ToastPeople.RemoteId"/> is specified, this property will be ignored. For more info,
            see the My People documentation.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastPeople.PhoneNumber">
            <summary>
            Gets or sets a phone number that corresponds with a contact in the local Windows ContactStore. Note
            that if <see cref="P:Microsoft.Toolkit.Uwp.Notifications.ToastPeople.EmailAddress"/> is specified, this property will be ignored.
            For more info, see the My People documentation.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBox">
            <summary>
            A selection box control, which lets users pick from a dropdown list of options.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBox.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBox"/> class.
            A Toast SelectionBox input control with the required elements.
            </summary>
            <param name="id">Developer-provided ID that the developer uses later to retrieve input when the Toast is interacted with.</param>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBox.Id">
            <summary>
            Gets the required ID property used so that developers can retrieve user input once the app is activated.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBox.Title">
            <summary>
            Gets or sets title text to display above the SelectionBox.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBox.DefaultSelectionBoxItemId">
            <summary>
            Gets or sets which item is selected by default, and refers to the Id property of <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBoxItem"/>. If you do not provide this, the default selection will be empty (user sees nothing).
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBox.Items">
            <summary>
            Gets the selection items that the user can pick from in this SelectionBox. Only 5 items can be added.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBoxItem">
            <summary>
            A selection box item (an item that the user can select from the drop down list).
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBoxItem.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBoxItem"/> class.
            Constructs a new Toast SelectionBoxItem with the required elements.
            </summary>
            <param name="id">Developer-provided ID that the developer uses later to retrieve input when the Toast is interacted with.</param>
            <param name="content">String that is displayed on the selection item. This is what the user sees.</param>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBoxItem.Id">
            <summary>
            Gets the required ID property used so that developers can retrieve user input once the app is activated.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastSelectionBoxItem.Content">
            <summary>
            Gets the required string that is displayed on the selection item.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastShoulderTapImage">
            <summary>
            Specifies the image to be displayed on a My People shoulder tap notification. New in Fall Creators Update.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastShoulderTapImage.Source">
            <summary>
            Gets or sets the URI of the image (Required). This will be used if the sprite sheet isn't provided, or
            if the sprite sheet cannot be loaded. Can be from your application package, application data, or the internet.
            Internet images must obey the toast image size restrictions.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastShoulderTapImage.SpriteSheet">
            <summary>
            Gets or sets an optional sprite sheet that can be used instead of the image to display an animated sprite sheet.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastShoulderTapImage.AlternateText">
            <summary>
            Gets or sets a description of the image, for users of assistive technologies.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastShoulderTapImage.AddImageQuery">
            <summary>
            Gets or sets a value whether Windows should append a query string to the image URI supplied in the <see cref="P:Microsoft.Toolkit.Uwp.Notifications.ToastShoulderTapImage.Source"/> property.
            Use this attribute if your server hosts images and can handle query strings, either by retrieving an image variant based on the
            query strings or by ignoring the query string and returning the image as specified without the query string.
            This query string specifies scale, contrast setting, and language.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastSpriteSheet">
            <summary>
            Specifies a sprite sheet. New in Fall Creators Update.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastSpriteSheet.Source">
            <summary>
            Gets or sets the URI of the sprite sheet (Required).
            Can be from your application package, application data, or the internet.
            Internet sources must obey the toast image size restrictions.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastSpriteSheet.FrameHeight">
            <summary>
            Gets or sets the frame-height of the sprite sheet. Required value that must be greater than 0.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastSpriteSheet.Fps">
            <summary>
            Gets or sets the frames per second at which to animate the sprite sheet. Required value that must be greater than 0 but no larger than 120.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastSpriteSheet.StartingFrame">
            <summary>
            Gets or sets the starting frame of the sprite sheet. If not specified, it will start at frame zero.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastTextBox">
            <summary>
            A text box control on the Toast that a user can type text into.
            </summary>
        </member>
        <member name="M:Microsoft.Toolkit.Uwp.Notifications.ToastTextBox.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Toolkit.Uwp.Notifications.ToastTextBox"/> class.
            A new Toast TextBox input control with the required elements.
            </summary>
            <param name="id">Developer-provided ID that the developer uses later to retrieve input when the Toast is interacted with.</param>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastTextBox.Id">
            <summary>
            Gets the required ID property so that developers can retrieve user input once the app is activated.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastTextBox.Title">
            <summary>
            Gets or sets title text to display above the text box.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastTextBox.PlaceholderContent">
            <summary>
            Gets or sets placeholder text to be displayed on the text box when the user hasn't typed any text yet.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastTextBox.DefaultInput">
            <summary>
            Gets or sets the initial text to place in the text box. Leave this null for a blank text box.
            </summary>
        </member>
        <member name="T:Microsoft.Toolkit.Uwp.Notifications.ToastVisual">
            <summary>
            Defines the visual aspects of a Toast notification.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastVisual.Language">
            <summary>
            Gets or sets the target locale of the XML payload, specified as BCP-47 language tags such as "en-US" or "fr-FR". This locale is overridden by any locale specified in binding or text. If this value is a literal string, this attribute defaults to the user's UI language. If this value is a string reference, this attribute defaults to the locale chosen by Windows Runtime in resolving the string.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastVisual.BaseUri">
            <summary>
            Gets or sets a default base URI that is combined with relative URIs in image source attributes.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastVisual.AddImageQuery">
            <summary>
            Gets or sets a value whether Windows is allowed to append a query string to the image URI supplied in the Toast notification. Use this attribute if your server hosts images and can handle query strings, either by retrieving an image variant based on the query strings or by ignoring the query string and returning the image as specified without the query string. This query string specifies scale, contrast setting, and language.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastVisual.BindingGeneric">
            <summary>
            Gets or sets the generic Toast binding, which can be rendered on all devices. This binding is required and cannot be null.
            </summary>
        </member>
        <member name="P:Microsoft.Toolkit.Uwp.Notifications.ToastVisual.BindingShoulderTap">
            <summary>
            Gets or sets a binding for shoulder tap notifications, which integrate with My People. See the My People documentation for more info. New in Fall Creators Update.
            </summary>
        </member>
    </members>
</doc>
