﻿function Use-PythonInstallRequirementsToVENV {
    param (
        [Parameter(Mandatory,Position=0)]
        [String] $Folder,
        [String] $RequirementsFile,
        [Switch] $NavigateToFolder
    )

    if(-not(Confirm-PythonFolderIsVENV -Folder $Folder)){
        throw "Passed -Folder ($Folder) is not a Python VENV"
    }

    if([String]::IsNullOrEmpty($RequirementsFile)){
        $RequirementsFile = Join-Path $Folder -ChildPath 'requirements.txt'
    }

    if(-not(Test-Path -LiteralPath $RequirementsFile -PathType Leaf)){
        throw "RequirementsFile ($RequirementsFile) does not exist."
    }

    Use-PythonActivateVENVInFolder -Folder $Folder | Out-Null

    Push-Location -LiteralPath $Folder -StackName RVENV

    $PipParams = 'install','-r',"$RequirementsFile"
    & pip $PipParams

    if(-not$NavigateToFolder){
        Pop-Location -StackName RVENV
    }

}