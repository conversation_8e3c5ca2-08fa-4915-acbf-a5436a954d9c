<img src="./Assets/Images/ModuleIcon.png" alt="Description" width="140">

# Futuremotion Development Toolbox
[![PowerShell Gallery Version](https://img.shields.io/powershellgallery/v/FMDevToolbox)](https://www.powershellgallery.com/packages/FMDevToolbox/1.0.1)
[![License](https://img.shields.io/badge/license-MIT-green)](./LICENSE)

## Description

Provides a wide range of functions for automating development workflows and CLI tools, managing applications and computer settings, formatting and transforming data, and verifying data formats.


## Installation from the Powershell Gallery

```powershell
Install-Module FMDevToolbox
Import-Module FMDevToolbox
```


## Requirements

Powershell 7.1+


## Changelog

- **1.0.1** - 11-14-2024 - Initial Release. Some functions are incomplete and code quality is not consistent across all functions. Futher updates will come with major improvements.


## Documentation

Full Markdown Documentation Coming Soon.

## Function List

