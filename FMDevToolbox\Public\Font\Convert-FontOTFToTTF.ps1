using namespace System.Collections.Generic

function Convert-FontOTFToTTF {
    [CmdletBinding(DefaultParameterSetName="Path")]
    param (
        [Parameter(
            Mandatory,
            Position = 0,
            ValueFromPipeline,
            ValueFromPipelineByPropertyName,
            ParameterSetName = "Path",
            HelpMessage = "Path to one or more locations to process (Supports wildcards)."
        )]
        [SupportsWildcards()]
        [ValidateNotNullOrEmpty()]
        [String[]] $Path,

        [Parameter(
            Mandatory,
            Position = 0,
            ValueFromPipelineByPropertyName,
            ParameterSetName = "LiteralPath",
            HelpMessage = "Path to one or more locations to process."
        )]
        [ValidateScript({
            # Throw on invalid path
            if(-not(Test-Path -Path $_ -IsValid)){
                throw "The directory/file name '$_' is not valid."
            }
            # Throw on wildcard characters
            if($_ -match '[\?\*]'){
                throw "Wildcard characters *, ? are not acceptable."
            }
            $true
        })]
        [Alias('PSPath')]
        [ValidateNotNullOrEmpty()]
        [String[]] $LiteralPath,


        [Decimal] $ConversionTolerance,
        [int32] $TargetUPM,
        [String] $RecalculateBoundingBoxes,
        [String] $ReorderFontTables,
        [String] $ReplaceModifiedTimestamp,
        [Switch] $Overwrite,
        [Switch] $AutoConvert,
        [Int32] $MaxThreads = 16
    )

    begin {
        $cmdFtcli = Initialize-FontFoundryToolsVENV -FontToolsVersion FontFoundryTools
        $otfList = [HashSet[String]]@()
        $dirList = [HashSet[string]]@()
    }

    process {

        # Resolve paths / literal paths correctly
        $resolvedPaths = @()
        if ($psCmdlet.ParameterSetName -eq 'Path') {
            foreach ($curPath in $Path) {
                    if (!(Test-Path -Path $curPath)) {
                    $ex = [ItemNotFoundException]::new("Cannot find path '$curPath' because it does not exist.")
                    $category = [ErrorCategory]::ObjectNotFound
                    $errRecord = [ErrorRecord]::new($ex,'PathNotFound',$category,$curPath)
                    $psCmdlet.WriteError($errRecord)
                    continue
                }
                # Resolve any wildcards that might be in the path
                $provider = $null
                    $null = $resolvedPaths += $psCmdlet.SessionState.Path.GetResolvedProviderPathFromPSPath($curPath, [ref]$provider)
                }
        }
        else {
            foreach ($curPath in $LiteralPath) {
                    if (!(Test-Path -LiteralPath $curPath)) {
                    $ex = New-Object System.Management.Automation.ItemNotFoundException "Cannot find path '$curPath' because it does not exist."
                    $category = [System.Management.Automation.ErrorCategory]::ObjectNotFound
                    $errRecord = New-Object System.Management.Automation.ErrorRecord $ex,'PathNotFound',$category,$curPath
                    $psCmdlet.WriteError($errRecord)
                    continue
                }
                # Resolve any relative paths
                $null = $resolvedPaths += $psCmdlet.SessionState.Path.GetUnresolvedProviderPathFromPSPath($curPath)
            }
        }
        foreach ($path in $resolvedPaths) {
            $item = Get-Item -LiteralPath $path -Force
            if ($item.PSIsContainer) {
                $null = $dirList.Add($item.FullName)
            }
            elseif($item.Extension -eq '.otf'){
                $null = $otfList.Add($item.FullName)
            }
        }
    }

    end {
        # Use sensible defaults for OTF to TTF conversion and avoid dialogs.
        if($AutoConvert){
            Write-Verbose "AutoConvert was specified. Using hardcoded sensible defaults.`n"
            $ConversionTolerance      = 1
            $TargetUPM                = 2048
            $RecalculateBoundingBoxes = 'Yes'
            $ReorderFontTables        = 'Yes'
            $ReplaceModifiedTimestamp = 'No'
            $Overwrite                = $false
        }
        function IsValidValue($value,$min,$max,$aconvert) {
            return ($value -lt $min -or $value -gt $max -or -not $value)
        }
        function IsValidString($str,$aconvert) {
            return ([String]::IsNullOrWhiteSpace($str) -or $str -notin $validYesNoValues)
        }

        if(-not $AutoConvert){
            $needToSpecifyUPM              = IsValidValue -value $TargetUPM -min 16 -max 16384
            $needToSpecifyTolerance        = IsValidValue -value $ConversionTolerance -min 0 -max 3
            $needToSpecifyRecalcBBoxes     = IsValidString -str $RecalculateBoundingBoxes
            $needToSpecifyReorderTables    = IsValidString -str $ReorderFontTables
            $needToSpecifyReplaceTimestamp = IsValidString -str $ReplaceModifiedTimestamp
        }

        $recalcBBoxesHelpUrl           = "https://foundrytools-cli.readthedocs.io/en/latest/commands/converter.html#cmdoption-ftcli-converter-otf2ttf-no-rbb"
        $reorderTablesHelpUrl          = "https://foundrytools-cli.readthedocs.io/en/latest/commands/converter.html#cmdoption-ftcli-converter-ft2wf-rtb"
        $replaceTimestampHelpUrl       = "https://foundrytools-cli.readthedocs.io/en/latest/commands/converter.html#cmdoption-ftcli-converter-otf2ttf-rts"

        if($needToSpecifyTolerance){
            $DialogSplat = @{
                MainInstruction = "Please specify the Conversion tolerance [0.0<=x<=3.0]."
                MainContent     = "Enter a value between 0.0 and 3.0. Low tolerance adds more points but keeps shapes. High tolerance adds few points but may change shape."
                WindowTitle     = "OTF to TTF Conversion Tolerance"
                InputText       = 1.5
                Verbose         = $false
            }
            do {
                $Result = Invoke-OokiiInputDialog @DialogSplat
                if($Result.Result -eq 'Cancel'){ exit }
                [decimal] $ConversionTolerance = $Result.Input
                [Bool] $toleranceIsValid = ($ConversionTolerance -ge 0.0 -and $ConversionTolerance -le 3.0)
            } while (-not$toleranceIsValid)
        }

        if($needToSpecifyUPM){
            $UPMDialogSplat = @{
                MainInstruction = "Please specify a target UPM integer (Units Per EM) value for the converted font. (16-16384)"
                MainContent     = "Scaling is applied to the font after conversion to TrueType. With TrueType fonts, the most common UPM value is 2048. Other values, like 1000 or 4096, are sometimes used."
                WindowTitle     = "Target TTF UPM value (Units Per EM)"
                InputText       = 2048
                DialogWidth     = 280
                Verbose         = $false
            }
            do {
                $UPMResult = Invoke-OokiiInputDialog @UPMDialogSplat
                if($UPMResult.Result -eq 'Cancel'){ exit }
                [int32] $TargetUPM = $UPMResult.Input
                [Bool] $UPMIsValid = ($TargetUPM -ge 16 -and $TargetUPM -le 16384)
            } while (-not$UPMIsValid)
        }

        if($needToSpecifyRecalcBBoxes){
            $bboxButtons = [System.Collections.ArrayList]@()
            $null = $bboxButtons.Add('Yes')
            $null = $bboxButtons.Add('No')
            $recalcBBoxTaskDialogSplat = @{
                MainInstruction           = "Recalculate the font's bounding boxes on save?"
                MainContent               = "Choose whether to recalculate 'glyf', 'CFF', 'hhea'/'vhea', 'min/max', and 'head' bounding box values."
                MainButtonStyle           = 'CommandLinks'
                MainIcon                  = 'Information'
                WindowTitle               = 'Recalculate Bounding Boxes'
                MainButtons               = $bboxButtons
                FooterText                = "More information about this setting here: <a href=`"$recalcBBoxesHelpUrl`">ftcli converter documentation</a>"
                ExpandedInfo              = "By default, 'glyf', 'CFF', annd 'head' bounding box values and 'hhea'/'vhea' min/max values are not recalculated on save. When this option is active, the glyphs are compiled on importing, which saves memory consumption and time."
                DialogWidth               = 320
                ExpandedInfoOpenByDefault = $true
                AllowCancel               = $true
                Verbose                   = $false
            }
            $RecalcBBoxResult = Invoke-OokiiTaskDialog @recalcBBoxTaskDialogSplat
            if(-not $RecalcBBoxResult){ exit }
            $RecalculateBoundingBoxes = $RecalcBBoxResult.Text
        }

        if($needToSpecifyReorderTables){
            $reorderTablesButtons = [System.Collections.ArrayList]@()
            $reorderTablesButtons.Add('Yes')
            $reorderTablesButtons.Add('No')
            $reorderTablesDialogSplat = @{
                MainInstruction = "Reorder the font's tables on save?"
                MainContent     = "When this option is active, tables are sorted by tag on save (recommended by the OpenType specification)."
                MainButtonStyle = 'CommandLinks'
                MainIcon        = 'Information'
                WindowTitle     = 'Reorder font tables'
                MainButtons     = $reorderTablesButtons
                FooterText      = "More information about this setting here: <a href=`"$reorderTablesHelpUrl`">ftcli converter documentation</a>"
                ExpandedInfo    = $null
                DialogWidth     = 320
                AllowCancel     = $true
                Verbose         = $false
            }
            $reorderTablesResult = Invoke-OokiiTaskDialog @reorderTablesDialogSplat
            if(-not $reorderTablesResult){ exit }
            $ReorderFontTables = $reorderTablesResult.Text
        }

        if($needToSpecifyReplaceTimestamp){
            $replaceTimestampButtons = [System.Collections.ArrayList]@()
            $replaceTimestampButtons.Add('Yes')
            $replaceTimestampButtons.Add('No')
            $replaceTimestampDialogSplat = @{
                MainInstruction = "Replace the font's 'modified' timestamp in the 'head' table?"
                MainContent     = "By default, the original 'modified' timestamp is kept."
                MainButtonStyle = 'CommandLinks'
                MainIcon        = 'Information'
                WindowTitle     = 'Replace modified timestamp'
                MainButtons     = $replaceTimestampButtons
                FooterText      = "More information about this setting here: <a href=`"$replaceTimestampHelpUrl`">ftcli converter documentation</a>"
                ExpandedInfo    = $null
                DialogWidth     = 320
                AllowCancel     = $true
                Verbose         = $false
            }
            $replaceTimestampResult = Invoke-OokiiTaskDialog @replaceTimestampDialogSplat
            if(-not $replaceTimestampResult){ exit }
            $ReplaceModifiedTimestamp = $replaceTimestampResult.Text
        }

        if($dirList.Count -gt 0){
            foreach ($curDir in $dirList) {
                gci -LP $curDir -Force -File -ErrorAction 0 | ? { $_.Extension -eq '.otf'} | % {
                    $null = $otfList.Add($_.FullName)
                }
            }
        }

        if(-not $AutoConvert){
            $overwriteLabel = ($Overwrite) ? 'Yes' : 'No'
            Write-Verbose "Converting fonts with the following options:"
            Write-Verbose "──────────────────────────────────────────────────────────────────────"
            Write-Verbose "Font Conversion Tolerance:           $ConversionTolerance"
            Write-Verbose "Font Target UPM:                     $TargetUPM"
            Write-Verbose "Recalculate Font's Bounding Boxes?   $RecalculateBoundingBoxes"
            Write-Verbose "Reorder Font's Tables?               $ReorderFontTables"
            Write-Verbose "Replace Font's 'modified' timestamp? $ReplaceModifiedTimestamp"
            Write-Verbose "Overwrite fonts with the same name?  $overwriteLabel"
            Write-Verbose "`n"
        }
        else {
            Write-Verbose "AutoConvert was specified. Converting fonts with default settings."
            Write-Verbose "──────────────────────────────────────────────────────────────────────"
            Write-Verbose "Font Conversion Tolerance:            1"
            Write-Verbose "Font Target UPM:                      2048"
            Write-Verbose "Recalculate Font's Bounding Boxes?    Yes"
            Write-Verbose "Reorder Font's Tables?                Yes"
            Write-Verbose "Replace Font's 'modified' timestamp?  No"
            Write-Verbose "Overwrite fonts with the same name?   No"
            Write-Verbose "`n"
        }

        $otfList | % -Parallel {
            $currentFont         = $_
            $cmdFtcli            = $Using:cmdFtcli
            $tolerance           = $Using:ConversionTolerance
            $targetUPM           = $Using:TargetUPM
            $recalculateBBoxes   = $Using:RecalculateBoundingBoxes
            $reorderFontTables   = $Using:ReorderFontTables
            $replaceTimestamp    = $Using:ReplaceModifiedTimestamp
            $doOverwrite         = $Using:Overwrite

            $ftcliParams = "converter", "otf2ttf", "--tolerance", $tolerance, "--target-upm", $targetUPM
            if($recalculateBBoxes -eq 'Yes') { $ftcliParams += '--recalc-bboxes' }
            if($reorderFontTables -eq 'Yes') { $ftcliParams += '--reorder-tables' }
            if($replaceTimestamp -eq 'Yes')  { $ftcliParams += '--recalc-timestamp' }
            if(-not $doOverwrite)            { $ftcliParams += '--no-overwrite' }
            $ftcliParams += $currentFont
            & $cmdFtcli $ftcliParams

        } -ThrottleLimit $MaxThreads

        & deactivate
    }
}