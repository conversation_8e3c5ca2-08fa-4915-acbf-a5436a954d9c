// https://eslint.org/docs/latest/use/configure/configuration-files
import { defineConfig, globalIgnores } from "eslint/config";
import js from "@eslint/js";

export default defineConfig([
    globalIgnores([".config/", "dist/", "tsconfig.json", "__tests/**", "working/**", "reference/**"]),
    {
        // https://eslint.org/docs/latest/use/configure/language-options#specifying-javascript-options
		languageOptions: {
			ecmaVersion: latest,
			sourceType: "commonjs"
		},
	},
    { files: ["**/*.{js,mjs,cjs}"],  plugins: { js },  extends: ["js/recommended"] },
    { files: ["**/*.js"],  plugins: { js,},  extends: ["js/recommended"], },
    {
		rules: {
			semi: "error",
            "no-unused-vars": "warn",
            "prefer-const": "warn"
		},
	},
])