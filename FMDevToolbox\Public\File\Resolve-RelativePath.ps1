﻿function Resolve-RelativePath {
    <#
    .SYNOPSIS
    Resolves a relative path to an absolute path based on a specified root path.

    .DESCRIPTION
    The Resolve-RelativePath function takes a relative path and a root path, combines them,
    and resolves the resulting path to an absolute path. It also provides an option to resolve symbolic links.

    .PARAMETER RelativePath
    The relative path that needs to be resolved into an absolute path.

    .PARAMETER RootPath
    The root path from which the relative path should be resolved. Defaults to the current directory if not specified.

    .PARAMETER ResolveSymlinks
    A switch to indicate if symbolic links should be resolved to their target paths.

    .OUTPUTS
    System.String

    .EXAMPLE
    # **Example 1**
    # This example demonstrates how to resolve a relative path using the current directory as the root.
    Resolve-RelativePath -RelativePath "..\Documents\file.txt"

    .EXAMPLE
    # **Example 2**
    # This example demonstrates how to resolve a relative path with a specified root path.
    Resolve-RelativePath -RelativePath "Projects\project1" -RootPath "C:\Users\<USER>\shortcut" -RootPath "C:\Users\<USER>